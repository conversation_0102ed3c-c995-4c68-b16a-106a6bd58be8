// script.js
// CSS-Stil für .content-scroll hinzufügen, um sicherzustellen, dass Scroll-Verhalten korrekt ist
const styleEl = document.createElement('style');
styleEl.textContent = `
    .content-scroll {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: auto;
        max-height: calc(80vh - 60px);
    }
    .modal:not(.active) .content-scroll {
        scrollTop: 0 !important;
    }
`;
document.head.appendChild(styleEl);

// Funktion zum Zurücksetzen der Scroll-Position bei Modals
function resetModalScroll(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        const contentScroll = modal.querySelector('.content-scroll');
        if (contentScroll) {
            contentScroll.scrollTop = 0;
        }
    }
}

// Direktes Hinzufügen von Event-Listenern für das Zurücksetzen der Scroll-Position
document.addEventListener('DOMContentLoaded', function() {
    // Überwachen der Aktivität von Modals - wenn sie geschlossen werden
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                const modal = mutation.target;
                if (!modal.classList.contains('active')) {
                    // Wenn das Modal nicht mehr aktiv ist (geschlossen wurde)
                    if (modal.id === 'ueber-mich-modal' || modal.id === 'impressum-modal') {
                        const contentScroll = modal.querySelector('.content-scroll');
                        if (contentScroll) {
                            // Sofort zurücksetzen
                            contentScroll.scrollTop = 0;
                        }
                    }
                }
            }
        });
    });

    // Beobachten der Modals
    const impressumModal = document.getElementById('impressum-modal');
    const ueberMichModal = document.getElementById('ueber-mich-modal');
    
    if (impressumModal) {
        observer.observe(impressumModal, { attributes: true });
    }
    
    if (ueberMichModal) {
        observer.observe(ueberMichModal, { attributes: true });
    }
});

// script.js - Optimierte Version
document.addEventListener('DOMContentLoaded', function() {
// Prüfen, ob die erforderlichen Daten geladen wurden
if (typeof slidesData === 'undefined' || typeof modalsData === 'undefined') {
    console.error('Fehler: Daten nicht verfügbar. Bitte stellen Sie sicher, dass die Daten korrekt geladen werden.');
    return;
}

// Variable für das speichern des zuletzt fokussierten Elements
let lastFocusedElement = null;

// Funktion zur Tastaturunterstützung
function setupKeyboardNavigation() {
    // Tab-Index für alle interaktiven Elemente hinzufügen
    document.querySelectorAll('.hotspot, .carousel-arrow, .carousel-dot').forEach(element => {
        if (!element.getAttribute('tabindex')) {
            element.setAttribute('tabindex', '0');
            element.setAttribute('role', 'button');
        }
    });
    
    // ARIA-Labels für bessere Accessibility
    document.querySelectorAll('.hotspot').forEach(hotspot => {
        hotspot.setAttribute('aria-label', 'Informationspunkt öffnen');
    });
    
    document.querySelector('.prev-arrow').setAttribute('aria-label', 'Vorheriges Bild');
    document.querySelector('.next-arrow').setAttribute('aria-label', 'Nächstes Bild');
    
    document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
        dot.setAttribute('aria-label', `Zu Bild ${index + 1} wechseln`);
    });

    // Keydown-Event für interaktive Elemente und Modals
    document.addEventListener('keydown', function(e) {
        const activeElement = document.activeElement;
        
        // Enter oder Space für klickbare Elemente
        if ((e.key === 'Enter' || e.key === ' ') && 
            (activeElement.classList.contains('hotspot') || 
             activeElement.classList.contains('carousel-arrow') || 
             activeElement.classList.contains('carousel-dot') ||
             activeElement.classList.contains('close-modal') ||
             activeElement.classList.contains('cta-button') ||
             activeElement.classList.contains('social-link'))) {
            
            e.preventDefault(); // Verhindert Scrollen bei Space
            activeElement.click(); // Simuliert einen Klick
        }
        
        // Pfeilnavigation für Carousel
        if (document.activeElement.tagName !== 'INPUT') {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                document.querySelector('.prev-arrow').click();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                document.querySelector('.next-arrow').click();
            }
        }
    });
    
    // Fokus-Management für Modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('keydown', function(e) {
            if (e.key === 'Tab' && modal.classList.contains('active')) {
                const focusableElements = modal.querySelectorAll('button:not([tabindex="-1"]), [href], input:not([tabindex="-1"]), select:not([tabindex="-1"]), textarea:not([tabindex="-1"]), [tabindex]:not([tabindex="-1"])');
                
                if (focusableElements.length) {
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];
                    
                    // Tab-Fokus in Modal einschließen
                    if (e.shiftKey && document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    } else if (!e.shiftKey && document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });
    });
}

// Hilfsfunktionen für Fokusmanagement
function saveFocus() {
    lastFocusedElement = document.activeElement;
}

function restoreFocus() {
    if (lastFocusedElement) {
        setTimeout(() => {
            lastFocusedElement.focus();
        }, 10);
    }
}

// Prüfen, ob ein Modal über URL-Parameter geöffnet werden soll
function checkUrlForModalOpening() {
    const urlParams = new URLSearchParams(window.location.search);
    const modalParam = urlParams.get('modal');
    
    if (modalParam) {
        // URL-Parameter aus der URL entfernen (ohne Neuladen der Seite)
        window.history.replaceState({}, document.title, window.location.pathname);
        
        // Entsprechendes Modal öffnen
        setTimeout(() => {
            switch(modalParam) {
                case 'ueber-mich':
                    const ueberMichModal = document.getElementById('ueber-mich-modal');
                    if (ueberMichModal) {
                        saveFocus();
                        ueberMichModal.classList.add('active');
                        ueberMichModal.setAttribute('aria-hidden', 'false');
                        // Scroll-Position zurücksetzen
                        const contentScroll = ueberMichModal.querySelector('.content-scroll');
                        if (contentScroll) contentScroll.scrollTop = 0;
                        
                        // Fokus auf ersten interaktiven Element
                        const closeButton = ueberMichModal.querySelector('.close-modal');
                        if (closeButton) closeButton.focus();
                    }
                    break;
                case 'impressum':
                    const impressumModal = document.getElementById('impressum-modal');
                    if (impressumModal) {
                        saveFocus();
                        impressumModal.classList.add('active');
                        impressumModal.setAttribute('aria-hidden', 'false');
                        // Scroll-Position zurücksetzen
                        const contentScroll = impressumModal.querySelector('.content-scroll');
                        if (contentScroll) contentScroll.scrollTop = 0;
                        
                        // Fokus auf ersten interaktiven Element
                        const closeButton = impressumModal.querySelector('.close-modal');
                        if (closeButton) closeButton.focus();
                    }
                    break;
            }
        }, 500); // Kurze Verzögerung, um sicherzustellen, dass die Seite vollständig geladen ist
    }
}

// Aufruf der Funktion nach dem Laden der Seite
checkUrlForModalOpening();

// Referenzen zu Containern
const carouselContainer = document.getElementById('carousel-container');
const modalsContainer = document.getElementById('modals-container');

// Carousel-Slides generieren
let slidesHTML = '';

slidesData.forEach((slide, index) => {
    // Aktiv-Status für erste Slide
    const activeClass = index === 0 ? 'active' : '';
    
    // HTML für Hotspots generieren
    let hotspotsHTML = '';
    slide.hotspots.forEach(hotspot => {
        hotspotsHTML += `<div class="hotspot" id="${hotspot.id}" data-modal="${hotspot.modalId}" tabindex="0" role="button" aria-label="Informationspunkt öffnen">+</div>`;
    });
    
    // HTML für den Slide zusammenbauen
    slidesHTML += `
        <div class="carousel-slide ${activeClass}" id="${slide.id}" aria-hidden="${index === 0 ? 'false' : 'true'}">
            <img src="${slide.image}" alt="${slide.altText}" class="main-image" id="${slide.imageId}">
            <div class="category-title">${slide.title}</div>
            ${hotspotsHTML}
        </div>
    `;
});

// Carousel-Navigation hinzufügen
slidesHTML += `
    <div class="carousel-arrow prev-arrow" tabindex="0" role="button" aria-label="Vorheriges Bild">&#10094;</div>
    <div class="carousel-arrow next-arrow" tabindex="0" role="button" aria-label="Nächstes Bild">&#10095;</div>
    
    <div class="carousel-dots" role="tablist">
        ${slidesData.map((_, index) => `
            <div class="carousel-dot ${index === 0 ? 'active' : ''}" data-slide="${index}" tabindex="0" role="tab" aria-selected="${index === 0 ? 'true' : 'false'}" aria-label="Zu Bild ${index + 1} wechseln"></div>
        `).join('')}
    </div>
`;

// Carousel-HTML in Container einfügen
carouselContainer.innerHTML = slidesHTML;

// Modals generieren
let modalsHTML = '';

modalsData.forEach(modal => {
    let ctaButtonHTML = '';
    if (modal.id === 'modal-zwischenmenschlichkeit-0' && modal.ctaText) {
        ctaButtonHTML = `<a href="${modal.ctaLink}" class="cta-button" tabindex="0">→ ${modal.ctaText}</a>`;
    } else if (modal.id === 'modal-fuehrung-1' && modal.ctaText) {
        ctaButtonHTML = `<a href="${modal.ctaLink}" class="cta-button" target="_blank" rel="noopener noreferrer" tabindex="0">${modal.ctaText}</a>`;
    }

    modalsHTML += `
        <div class="modal" id="${modal.id}" role="dialog" aria-labelledby="${modal.id}-title" aria-hidden="true">
            <div class="modal-content">
                <button class="close-modal" tabindex="-1" aria-label="Schließen">&times;</button>
                <h2 id="${modal.id}-title">${modal.title}</h2>
                ${modal.content}
                ${ctaButtonHTML}
            </div>
        </div>
    `;
});

// Modals-HTML in Container einfügen
modalsContainer.innerHTML = modalsHTML;

// CTA-Button im ersten Modal klickbar machen
const firstModalCta = document.querySelector('#modal-zwischenmenschlichkeit-0 .cta-button');
if (firstModalCta) {
    firstModalCta.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Alle modals schließen
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('active');
            modal.setAttribute('aria-hidden', 'true');
            const connector = modal.querySelector('.modal-connector');
            if (connector) connector.style.display = 'none';
        });
        
        // Zur letzten Slide springen
        showSlide(slidesData.length - 1);
        
        // Kurze Verzögerung, dann den Hotspot-Klick simulieren
        setTimeout(() => {
            const lastHotspot = document.getElementById('hotspot-fuehrung-1');
            if (lastHotspot) {
                lastHotspot.click();
            }
        }, 500);
    });
}

// Carousel-Funktionalität
const slides = document.querySelectorAll('.carousel-slide');
const prevArrow = document.querySelector('.prev-arrow');
const nextArrow = document.querySelector('.next-arrow');
const dots = document.querySelectorAll('.carousel-dot');
let currentSlide = 0;

function showSlide(index) {
    // Update slides
    slides.forEach((slide, i) => {
        slide.classList.remove('active');
        slide.setAttribute('aria-hidden', 'true');
    });
    
    if (index < 0) {
        currentSlide = slides.length - 1;
    } else if (index >= slides.length) {
        currentSlide = 0;
    } else {
        currentSlide = index;
    }
    
    slides[currentSlide].classList.add('active');
    slides[currentSlide].setAttribute('aria-hidden', 'false');
    
    // Update dots
    dots.forEach((dot, i) => {
        if (i === currentSlide) {
            dot.classList.add('active');
            dot.setAttribute('aria-selected', 'true');
            dot.setAttribute('tabindex', '0');
        } else {
            dot.classList.remove('active');
            dot.setAttribute('aria-selected', 'false');
            dot.setAttribute('tabindex', '0');
        }
    });
    
    // Fokus auf aktuelle Slide-Punkte setzen
    document.querySelectorAll(`#${slides[currentSlide].id} .hotspot`).forEach(hotspot => {
        hotspot.setAttribute('tabindex', '0');
    });
}

// Event-Listener für Navigation
prevArrow.addEventListener('click', () => {
    showSlide(currentSlide - 1);
});

nextArrow.addEventListener('click', () => {
    showSlide(currentSlide + 1);
});

// Event-Listener für Dots
dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
        showSlide(index);
    });
});

// Hotspot-Funktionalität
const hotspots = document.querySelectorAll('.hotspot');
const modals = document.querySelectorAll('.modal');
const closeButtons = document.querySelectorAll('.close-modal');

// Verbesserte Funktion zur Berechnung der Modal-Position mit Sprechblaseneffekt
function calculateModalPosition(hotspot, modal) {
    const modalContent = modal.querySelector('.modal-content');
    
    // Connector erstellen oder abrufen
    let connector = modal.querySelector('.modal-connector');
    if (!connector) {
        connector = document.createElement('div');
        connector.className = 'modal-connector';
        modal.appendChild(connector);
    }
    
    // Hotspot-Position relativ zum Viewport ermitteln
    const hotspotRect = hotspot.getBoundingClientRect();
    
    // Carousel-Container-Position und -Dimensionen ermitteln
    const carouselContainer = document.getElementById('carousel-container');
    const carouselRect = carouselContainer.getBoundingClientRect();
    
    // Modal-Abmessungen
    const modalWidth = modalContent.offsetWidth;
    const modalHeight = modalContent.offsetHeight;
    
    // Abstand für Carousel-Dots am unteren Rand
    const dotsHeight = 50; // Höhe für die Carousel-Dots
    
    // Hotspot-Mittelpunkt
    const hotspotCenterX = hotspotRect.left + (hotspotRect.width / 2);
    const hotspotCenterY = hotspotRect.top + (hotspotRect.height / 2);
    
    // Konstanten für die Positionierung
    const padding = 20; // Abstand zwischen Modal und Viewport-Rand
    const hotspotOffset = 20; // Abstand zwischen Hotspot und Modal
    
    // Verfügbarer Raum innerhalb des Carousels berechnen
    const availableTop = hotspotRect.top - carouselRect.top;
    const availableBottom = carouselRect.bottom - dotsHeight - hotspotRect.bottom;
    const availableLeft = hotspotRect.left - carouselRect.left;
    const availableRight = carouselRect.right - hotspotRect.right;
    
    // Bestimmen, wo am meisten Platz ist (oben, unten, links, rechts)
    const spaces = [
        { direction: 'left', space: availableLeft },
        { direction: 'right', space: availableRight },
        { direction: 'top', space: availableTop },
        { direction: 'bottom', space: availableBottom }
    ];
    
    // Nach verfügbarem Platz sortieren (absteigend)
    spaces.sort((a, b) => b.space - a.space);
    
    // Präferenz: Horizontale Positionierung, wenn genug Platz vorhanden
    let preferredDirection = null;
    
    // Zuerst horizontale Richtungen prüfen
    for (const dir of spaces) {
        if ((dir.direction === 'left' && dir.space >= modalWidth + padding) ||
            (dir.direction === 'right' && dir.space >= modalWidth + padding)) {
            preferredDirection = dir.direction;
            break;
        }
    }
    
    // Wenn horizontal nicht genug Platz, dann vertikal prüfen
    if (!preferredDirection) {
        for (const dir of spaces) {
            if ((dir.direction === 'top' && dir.space >= modalHeight + padding) ||
                (dir.direction === 'bottom' && dir.space >= modalHeight + padding)) {
                preferredDirection = dir.direction;
                break;
            }
        }
    }
    
    // Wenn immer noch keine Richtung gefunden, dann die mit dem meisten Platz nehmen
    if (!preferredDirection) {
        preferredDirection = spaces[0].direction;
    }
    
    // Connector-Position setzen und Modal positionieren
    let modalLeft, modalTop;
    let connectorPosX, connectorPosY; // Position des Connectors im Viewport
    
    switch (preferredDirection) {
        case 'left':
            // Modal links vom Hotspot platzieren
            modalLeft = Math.max(
                carouselRect.left + padding,
                Math.min(hotspotRect.left - modalWidth - hotspotOffset, carouselRect.right - modalWidth - padding)
            );
            modalTop = Math.max(
                carouselRect.top + padding,
                Math.min(hotspotCenterY - (modalHeight / 2), carouselRect.bottom - dotsHeight - modalHeight - padding)
            );
            
            // Connector-Position (rechter Rand des Modals)
            connectorPosX = modalLeft + modalWidth;
            connectorPosY = hotspotCenterY;
            break;
            
        case 'right':
            // Modal rechts vom Hotspot platzieren
            modalLeft = Math.max(
                carouselRect.left + padding,
                Math.min(hotspotRect.right + hotspotOffset, carouselRect.right - modalWidth - padding)
            );
            modalTop = Math.max(
                carouselRect.top + padding,
                Math.min(hotspotCenterY - (modalHeight / 2), carouselRect.bottom - dotsHeight - modalHeight - padding)
            );
            
            // Connector-Position (linker Rand des Modals)
            connectorPosX = modalLeft;
            connectorPosY = hotspotCenterY;
            break;
            
        case 'top':
            // Modal oberhalb des Hotspots platzieren
            modalLeft = Math.max(
                carouselRect.left + padding,
                Math.min(hotspotCenterX - (modalWidth / 2), carouselRect.right - modalWidth - padding)
            );
            modalTop = Math.max(
                carouselRect.top + padding,
                Math.min(hotspotRect.top - modalHeight - hotspotOffset, carouselRect.bottom - dotsHeight - modalHeight - padding)
            );
            
            // Connector-Position (unterer Rand des Modals)
            connectorPosX = hotspotCenterX;
            connectorPosY = modalTop + modalHeight;
            break;
            
        case 'bottom':
            // Modal unterhalb des Hotspots platzieren
            modalLeft = Math.max(
                carouselRect.left + padding,
                Math.min(hotspotCenterX - (modalWidth / 2), carouselRect.right - modalWidth - padding)
            );
            modalTop = Math.max(
                carouselRect.top + padding,
                Math.min(hotspotRect.bottom + hotspotOffset, carouselRect.bottom - dotsHeight - modalHeight - padding)
            );
            
            // Connector-Position (oberer Rand des Modals)
            connectorPosX = hotspotCenterX;
            connectorPosY = modalTop;
            break;
    }
    
    // Position des Modals setzen
    modalContent.style.left = `${modalLeft}px`;
    modalContent.style.top = `${modalTop}px`;
    
    // Connector relativ zum Modal positionieren
    setTimeout(() => {
        // Connector-Position (absolut im Viewport)
        const connectorLeft = connectorPosX - 10; // 10 = halbe Connector-Breite
        const connectorTop = connectorPosY - 10; // 10 = halbe Connector-Höhe
        
        // Position setzen (absolut im Viewport)
        connector.style.left = `${connectorLeft}px`;
        connector.style.top = `${connectorTop}px`;
        connector.style.display = 'block';
    }, 50);
    
    return { left: modalLeft, top: modalTop };
}

// Event-Listener für Hotspots
hotspots.forEach(hotspot => {
    hotspot.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // Letztes fokussiertes Element speichern
        saveFocus();
        
        // Vorherige aktive Modals schließen
        modals.forEach(m => {
            m.classList.remove('active');
            m.setAttribute('aria-hidden', 'true');
            const connector = m.querySelector('.modal-connector');
            if (connector) connector.style.display = 'none';
        });
        document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
        
        // Modal ID und Element
        const modalId = hotspot.getAttribute('data-modal');
        const modal = document.getElementById(modalId);
        const modalContent = modal.querySelector('.modal-content');
        
        // Hotspot als aktiv markieren
        hotspot.classList.add('active');
        
        // Erst das Modal als "vorbereitet" markieren
        modal.classList.add('preparing');
        
        // Modal Inhalt unsichtbar machen, aber anzeigen für Messungen
        modalContent.style.opacity = '0';
        modal.style.display = 'block';
        
        // Position berechnen
        setTimeout(() => {
            const position = calculateModalPosition(hotspot, modal);
            
            // Jetzt erst das Modal wirklich anzeigen
            modal.classList.add('active');
            modal.setAttribute('aria-hidden', 'false');
            modal.classList.remove('preparing');
            modalContent.style.opacity = '';
            
            // Fokus auf das erste interaktive Element im Modal setzen
            const closeButton = modal.querySelector('.close-modal');
            if (closeButton) {
                closeButton.setAttribute('tabindex', '0');
                setTimeout(() => {
                    closeButton.focus();
                }, 100);
            }
        }, 10);
    });
    
    // Keyboard-Support für Hotspots
    hotspot.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            hotspot.click();
        }
    });
});

// Beim Ändern der Fenstergröße aktive Modals neu positionieren
window.addEventListener('resize', () => {
    hotspots.forEach(hotspot => {
        if (hotspot.classList.contains('active')) {
            const modalId = hotspot.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            const modalContent = modal.querySelector('.modal-content');
            
            calculateModalPosition(hotspot, modal);
        }
    });
});

// Modal schließen bei Klick auf Schließen-Button
closeButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const modal = button.closest('.modal');
        
        // Scroll-Position sofort zurücksetzen - VOR dem Schließen
        if (modal.id === 'ueber-mich-modal' || modal.id === 'impressum-modal') {
            const contentScroll = modal.querySelector('.content-scroll');
            if (contentScroll) {
                contentScroll.scrollTop = 0;
            }
        }
        
        modal.classList.remove('active');
        modal.setAttribute('aria-hidden', 'true');
        // Connector ausblenden
        const connector = modal.querySelector('.modal-connector');
        if (connector) connector.style.display = 'none';
        // Aktiven Hotspot zurücksetzen
        document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
        
        // Fokus wiederherstellen
        restoreFocus();
    });
    
    // Keyboard-Support für Schließen-Button
    button.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            button.click();
        }
    });
});

// Modal schließen bei Klick außerhalb des Modal-Inhalts
document.addEventListener('click', (e) => {
    const isHotspotClick = e.target.closest('.hotspot');
    const isImpressumClick = e.target.closest('#impressum-link');
    const isUeberMichClick = e.target.closest('#header-ueber-mich-link') || e.target.closest('#footer-ueber-mich-link');
    const isProfileImageModalClick = e.target.closest('.profile-image-modal');
    
    if (!isHotspotClick && !isImpressumClick && !isUeberMichClick && !isProfileImageModalClick) {
        modals.forEach(modal => {
            const modalContent = modal.querySelector('.modal-content');
            if (modal.classList.contains('active') && !modalContent.contains(e.target)) {
                // Scroll-Position sofort zurücksetzen - VOR dem Schließen
                if (modal.id === 'ueber-mich-modal' || modal.id === 'impressum-modal') {
                    const contentScroll = modal.querySelector('.content-scroll');
                    if (contentScroll) {
                        contentScroll.scrollTop = 0;
                    }
                }
                
                modal.classList.remove('active');
                modal.setAttribute('aria-hidden', 'true');
                // Connector ausblenden
                const connector = modal.querySelector('.modal-connector');
                if (connector) connector.style.display = 'none';
                // Aktiven Hotspot zurücksetzen
                document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
                
                // Fokus wiederherstellen
                restoreFocus();
            }
        });
    }
});

// Schließen mit Escape-Taste
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const activeModal = document.querySelector('.modal.active');
        if (activeModal) {
            // Scroll-Position zurücksetzen bei Seiten-Modals
            if (activeModal.id === 'ueber-mich-modal' || activeModal.id === 'impressum-modal') {
                const contentScroll = activeModal.querySelector('.content-scroll');
                if (contentScroll) {
                    contentScroll.scrollTop = 0;
                }
            }
            
            activeModal.classList.remove('active');
            activeModal.setAttribute('aria-hidden', 'true');
            
            // Connector ausblenden
            const connector = activeModal.querySelector('.modal-connector');
            if (connector) connector.style.display = 'none';
            
            // Aktiven Hotspot zurücksetzen
            document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
            
            // Fokus wiederherstellen
            restoreFocus();
        }
    }
});

// Impressum Modal Funktionalität
const impressumLink = document.getElementById('impressum-link');
const impressumModal = document.getElementById('impressum-modal');

// Event-Listener für Impressum-Link
impressumLink.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Letztes fokussiertes Element speichern
    saveFocus();
    
    // Alle anderen Modals schließen
    modals.forEach(m => {
        m.classList.remove('active');
        m.setAttribute('aria-hidden', 'true');
        const connector = m.querySelector('.modal-connector');
        if (connector) connector.style.display = 'none';
    });
    document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
    
    // Scroll-Position zurücksetzen - IMMER VOR dem Öffnen des Modals
    const contentScroll = impressumModal.querySelector('.content-scroll');
    if (contentScroll) contentScroll.scrollTop = 0;
    
    // Impressum Modal öffnen
    impressumModal.classList.add('active');
    impressumModal.setAttribute('aria-hidden', 'false');
    
    // Fokus auf Schließen-Button setzen
    const closeButton = impressumModal.querySelector('.close-modal');
    if (closeButton) {
        closeButton.setAttribute('tabindex', '0');
        setTimeout(() => {
            closeButton.focus();
        }, 100);
    }
});

// Über mich Modal Funktionalität
const headerUeberMichLink = document.getElementById('header-ueber-mich-link');
const footerUeberMichLink = document.getElementById('footer-ueber-mich-link');
const ueberMichModal = document.getElementById('ueber-mich-modal');

// Event-Listener für Header-Link - Zum ersten Slide springen
headerUeberMichLink.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Alle Modals schließen
    modals.forEach(m => {
        m.classList.remove('active');
        m.setAttribute('aria-hidden', 'true');
        const connector = m.querySelector('.modal-connector');
        if (connector) connector.style.display = 'none';
    });
    document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
    
    // Zum ersten Slide springen
    showSlide(0);
});

// Event-Listener für Footer-Link - Über-mich-Modal öffnen
footerUeberMichLink.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Letztes fokussiertes Element speichern
    saveFocus();
    
    // Alle anderen Modals schließen
    modals.forEach(m => {
        m.classList.remove('active');
        m.setAttribute('aria-hidden', 'true');
        const connector = m.querySelector('.modal-connector');
        if (connector) connector.style.display = 'none';
    });
    document.querySelectorAll('.hotspot').forEach(h => h.classList.remove('active'));
    
    // Scroll-Position zurücksetzen - IMMER VOR dem Öffnen des Modals
    const contentScroll = ueberMichModal.querySelector('.content-scroll');
    if (contentScroll) contentScroll.scrollTop = 0;
    
    // Über mich Modal öffnen
    ueberMichModal.classList.add('active');
    ueberMichModal.setAttribute('aria-hidden', 'false');
    
    // Fokus auf Schließen-Button setzen
    const closeButton = ueberMichModal.querySelector('.close-modal');
    if (closeButton) {
        closeButton.setAttribute('tabindex', '0');
        setTimeout(() => {
            closeButton.focus();
        }, 100);
    }
});

// Profilbild-Zoom-Funktionalität
const profileImage = document.querySelector('.profile-image');

if (profileImage) {
    // Container für das Modal-Bild erstellen
    const imageModalContainer = document.createElement('div');
    imageModalContainer.className = 'profile-image-modal';
    imageModalContainer.style.display = 'none';
    imageModalContainer.setAttribute('role', 'dialog');
    imageModalContainer.setAttribute('aria-hidden', 'true');
    document.body.appendChild(imageModalContainer);
    
    // CSS für Modal-Bild hinzufügen
    const style = document.createElement('style');
    style.textContent = `
        .profile-image {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .profile-image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 2000;
            display: flex;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .profile-image-modal.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        .modal-image-container {
            position: absolute;
            /* Position wird dynamisch gesetzt */
            transform: translate(-50%, -50%);
        }
        
        .modal-image {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #7c6cd8;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .modal-image {
                width: 200px;
                height: 200px;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Klick-Ereignis für das Profilbild hinzufügen
    profileImage.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Letztes fokussiertes Element speichern
        saveFocus();
        
        // Originalbild-Position ermitteln
        const rect = profileImage.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // Modal-Container mit Bild füllen und anzeigen
        imageModalContainer.innerHTML = `
            <div class="modal-image-container" style="left: ${centerX}px; top: ${centerY}px;">
                <img src="${profileImage.src}" alt="${profileImage.alt}" class="modal-image">
                <button class="close-modal" tabindex="0" aria-label="Schließen">&times;</button>
            </div>`;
        imageModalContainer.style.display = 'block';
        imageModalContainer.setAttribute('aria-hidden', 'false');
        
        // Kurze Verzögerung, damit der Browser die Anzeige rendert
        setTimeout(() => {
            imageModalContainer.classList.add('active');
            
            // Fokus auf Schließen-Button setzen
            const closeButton = imageModalContainer.querySelector('.close-modal');
            if (closeButton) {
                closeButton.focus();
            }
        }, 10);
        
        // Event-Handler für das Schließen hinzufügen
        function imageModalClickHandler(e) {
            // Nicht schließen, wenn auf den Schließen-Button geklickt wurde (wird separat behandelt)
            if (e.target.classList.contains('close-modal')) return;
            
            e.preventDefault();
            e.stopPropagation(); // Verhindert, dass der Klick das "Über mich"-Modal schließt
            closeImageModal();
        }
        
        imageModalContainer.addEventListener('click', imageModalClickHandler);
        
        // Event-Handler für den Schließen-Button
        const closeButton = imageModalContainer.querySelector('.close-modal');
        if (closeButton) {
            closeButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeImageModal();
            });
            
            // Keyboard-Support für den Schließen-Button
            closeButton.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    closeImageModal();
                }
            });
        }
        
        // Funktion zum Schließen des Bild-Modals
        function closeImageModal() {
            imageModalContainer.classList.remove('active');
            imageModalContainer.setAttribute('aria-hidden', 'true');
            
            // Nach der Übergangsanimation das Modal ausblenden
            setTimeout(() => {
                imageModalContainer.style.display = 'none';
            }, 300);
            
            // Event-Listener entfernen
            imageModalContainer.removeEventListener('click', imageModalClickHandler);
            
            // Fokus wiederherstellen
            restoreFocus();
        }
    });
    
    // Keyboard-Support für das Profilbild
    profileImage.setAttribute('tabindex', '0');
    profileImage.setAttribute('role', 'button');
    profileImage.setAttribute('aria-label', 'Profilbild vergrößern');
    
    profileImage.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            profileImage.click();
        }
    });
}

// Positioniere die Hotspots entsprechend der neuen Inhalte
setupHotspotPositions();

// Tastaturnavigation einrichten
setupKeyboardNavigation();
});

// Funktion zum Positionieren der Hotspots
function setupHotspotPositions() {
// NEUE ZWISCHENMENSCHLICHKEIT
positionHotspot('hotspot-zwischenmenschlichkeit-0', { top: '30%', left: '50%' });
positionHotspot('hotspot-zwischenmenschlichkeit-1', { top: '45%', left: '32%' });
positionHotspot('hotspot-zwischenmenschlichkeit-2', { top: '77%', left: '42%' });
positionHotspot('hotspot-zwischenmenschlichkeit-3', { top: '70%', left: '65%' });

// NEUE EMOTIONALITÄT
positionHotspot('hotspot-emotionalitaet-1', { top: '39%', left: '36%' });
positionHotspot('hotspot-emotionalitaet-2', { top: '58%', left: '62%' });

// NEUE SCHÖPFERKRAFT
positionHotspot('hotspot-schoepferkraft-1', { top: '47%', left: '33%' });
positionHotspot('hotspot-schoepferkraft-2', { top: '70%', left: '65%' });

// NEUER ZUSAMMENHALT
positionHotspot('hotspot-zusammenhalt-1', { top: '48%', left: '35%' });
positionHotspot('hotspot-zusammenhalt-2', { top: '65%', left: '69%' });

// NEUE FÜHRUNG - großer Hotspot in der Mitte
positionHotspot('hotspot-fuehrung-1', { top: '49%', left: '46%' });
}

// Hilfsfunktion zum Positionieren eines Hotspots
function positionHotspot(id, position) {
const hotspot = document.getElementById(id);
if (hotspot) {
    if (position.top) hotspot.style.top = position.top;
    if (position.left) hotspot.style.left = position.left;
}
}