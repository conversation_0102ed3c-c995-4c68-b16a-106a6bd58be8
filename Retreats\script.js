// DOM-Elemente
document.addEventListener('DOMContentLoaded', function() {
    // Retreat Cards
    const retreatCards = document.querySelectorAll('.retreat-card');
    
    // Modals
    const impressumModal = document.getElementById('impressum-modal');
    const aboutModal = document.getElementById('about-modal');
    const impressumLink = document.getElementById('impressum-link');
    const footerAboutLink = document.getElementById('footer-about-link');
    const closeButtons = document.querySelectorAll('.close-modal');
    
    // Event-Listener für Retreat-Karten (Flip-Effekt)
    retreatCards.forEach(card => {
        card.addEventListener('click', function() {
            this.classList.toggle('flipped');
        });
    });
    
    // Event-Listener für Modal-Öffnen
    impressumLink.addEventListener('click', function(e) {
        e.preventDefault();
        openModal(impressumModal);
    });
    
    footerAboutLink.addEventListener('click', function(e) {
        e.preventDefault();
        openModal(aboutModal);
    });
    
    // Event-Listener für Modal-Schließen über Button
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            closeModal(this.closest('.modal'));
        });
    });
    
    // Event-Listener für Modal-Schließen über Außenbereich
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });
    
    // Event-Listener für Modal-Schließen über Escape-Taste
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
    
    // Mobile-Erkennung und Behandlung
    window.addEventListener('resize', adjustForMobile);
    adjustForMobile(); // Initial aufrufen
    
    // Touch-Ereignisse für mobile Geräte
    retreatCards.forEach(card => {
        // Verhindern von Zoom bei Doppeltipp auf mobilen Geräten
        card.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
    });
});

// Modal öffnen
function openModal(modal) {
    if (!modal) return;
    
    // Vorhandene Modals schließen
    closeAllModals();
    
    // Scroll-Position zurücksetzen
    const contentScroll = modal.querySelector('.content-scroll');
    if (contentScroll) {
        contentScroll.scrollTop = 0;
    }
    
    // Modal anzeigen
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Modal schließen
function closeModal(modal) {
    if (!modal) return;
    
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

// Alle Modals schließen
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        closeModal(modal);
    });
}

// Anpassungen für mobile Geräte
function adjustForMobile() {
    const isMobile = window.innerWidth <= 768;
    const isLandscape = window.innerWidth > window.innerHeight;
    const retreatGrid = document.querySelector('.retreat-grid');
    
    if (isMobile) {
        if (isLandscape) {
            retreatGrid.style.gridTemplateColumns = '1fr 1fr';
            retreatGrid.style.gridTemplateRows = '1fr 1fr';
        } else {
            retreatGrid.style.gridTemplateColumns = '1fr';
            retreatGrid.style.gridTemplateRows = 'repeat(4, 1fr)';
        }
    } else {
        retreatGrid.style.gridTemplateColumns = '1fr 1fr';
        retreatGrid.style.gridTemplateRows = '1fr 1fr';
    }
    
    // Textanpassung für kleinere Bildschirme
    const cardTitles = document.querySelectorAll('.card-title');
    cardTitles.forEach(title => {
        if (isMobile && !isLandscape) {
            title.style.fontSize = '1.2rem';
            title.style.bottom = '20px';
        } else {
            title.style.fontSize = '1.5rem';
            title.style.bottom = '30px';
        }
    });
}

// Verhindere Pinch-Zoom auf der gesamten Seite
document.addEventListener('touchmove', function(e) {
    if (e.touches.length > 1) {
        e.preventDefault();
    }
}, { passive: false });

// Verbessere Performance auf mobilen Geräten
const retreatCards = document.querySelectorAll('.retreat-card');

retreatCards.forEach(card => {
    card.addEventListener('touchstart', handleTouchStart, { passive: true });
    card.addEventListener('touchend', handleTouchEnd, { passive: true });
});

let touchStartTime = 0;
function handleTouchStart() {
    touchStartTime = Date.now();
}

function handleTouchEnd(e) {
    const touchDuration = Date.now() - touchStartTime;
    
    // Nur bei kurzem Tippen umdrehen (verhindert versehentliches Umdrehen beim Scrollen)
    if (touchDuration < 300) {
        this.classList.toggle('flipped');
    }
}