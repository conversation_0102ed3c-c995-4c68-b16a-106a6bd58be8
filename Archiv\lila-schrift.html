<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif;
        }
        
        :root {
            --primary-color: #7c6cd8;
            --secondary-color: #f3f0ff;
            --text-color: #333;
            --light-text: #666;
            --background: #fff;
            --transition: all 0.3s ease;
        }
        
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            color: var(--text-color);
            background-color: var(--background);
        }
        
        .header {
            background-color: rgba(255, 255, 255, 0.95);
            color: var(--primary-color);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            position: fixed;
            width: 100%;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            font-family: 'Playfair Display', serif;
            transition: var(--transition);
        }
        
        .main-image-container {
            margin-top: 60px;
            position: relative;
            flex-grow: 1;
            width: 100%;
            height: calc(100vh - 110px);
            overflow: hidden;
        }
        
        .carousel-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .carousel-slide {
            width: 100%;
            height: 100%;
            position: absolute;
            opacity: 0;
            transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .carousel-slide.active {
            opacity: 1;
        }
        
        .main-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            filter: brightness(0.9);
        }
        
        .carousel-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .carousel-arrow:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.05);
        }
        
        .prev-arrow {
            left: 20px;
        }
        
        .next-arrow {
            right: 20px;
        }
        
        .carousel-dots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 12px;
            z-index: 10;
        }
        
        .carousel-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .carousel-dot.active {
            background-color: white;
            transform: scale(1.2);
        }
        
        .footer {
            background-color: rgba(255, 255, 255, 0.95);
            color: var(--light-text);
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 300;
            letter-spacing: 0.5px;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 0 15px;
            text-align: center;
            flex-wrap: wrap;
        }
        
        .footer a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 10px;
            transition: var(--transition);
        }
        
        .footer a:hover {
            text-decoration: none;
            opacity: 0.8;
        }
        
        .hotspot {
            position: absolute;
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            cursor: pointer;
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 5;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }
        
        .hotspot:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .hotspot::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 50%;
            z-index: -1;
            animation: pulse 2s infinite;
            opacity: 0.6;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.6;
            }
            70% {
                transform: scale(1.5);
                opacity: 0;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            z-index: 999;
            pointer-events: none;
            backdrop-filter: blur(5px);
        }
        
        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }
        
        .modal-content {
            background-color: white;
            padding: 40px;
            max-width: 450px;
            width: 90%;
            border-radius: 15px;
            position: relative;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            transform: scale(0.9) translateY(20px);
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s;
            opacity: 0;
        }
        
        .modal.active .modal-content {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 22px;
            cursor: pointer;
            background: none;
            border: none;
            color: var(--light-text);
            transition: var(--transition);
            z-index: 10;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .close-modal:hover {
            color: var(--primary-color);
            background-color: var(--secondary-color);
        }
        
        h2 {
            margin-bottom: 20px;
            color: var(--primary-color);
            font-family: 'Playfair Display', serif;
            font-weight: 600;
            font-size: 28px;
        }
        
        p {
            line-height: 1.7;
            margin-bottom: 20px;
            color: var(--light-text);
            font-weight: 300;
            font-size: 16px;
        }
        
        .cta-button {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            font-size: 15px;
            letter-spacing: 0.5px;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(124, 108, 216, 0.3);
        }
        
        .cta-button:hover {
            background-color: #6a5bbb;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(124, 108, 216, 0.4);
        }
        
        .category-title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 42px;
            font-weight: 700;
            color: white;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            z-index: 5;
            font-family: 'Playfair Display', serif;
            letter-spacing: 2px;
            line-height: 1.4;
            max-width: 80%;
            opacity: 0;
            transition: opacity 0.5s ease, transform 0.5s ease;
            transform: translate(-50%, -40%);
            background: linear-gradient(135deg, rgba(124, 108, 216, 0.8), rgba(124, 108, 216, 0.4));
            padding: 20px 40px;
            border-radius: 10px;
            backdrop-filter: blur(4px);
        }
        
        .carousel-slide.active .category-title {
            opacity: 1;
            transform: translate(-50%, -50%);
            transition-delay: 0.3s;
        }

        /* Hotspot positioning für die verschiedenen Slides */
        /* Slide 1 - NEUE ZWISCHENMENSCHLICHKEIT */
        #slide1-hotspot1 { top: 25%; left: 30%; }
        #slide1-hotspot2 { top: 45%; left: 50%; }
        #slide1-hotspot3 { top: 65%; left: 70%; }
        
        /* Slide 2 - NEUE ARBEITSWELT */
        #slide2-hotspot1 { top: 30%; left: 25%; }
        #slide2-hotspot2 { top: 50%; left: 45%; }
        #slide2-hotspot3 { top: 70%; left: 65%; }
        
        /* Slide 3 - NEUE KINDERBEZIEHUNG */
        #slide3-hotspot1 { top: 35%; left: 35%; }
        #slide3-hotspot2 { top: 60%; left: 65%; }
        
        /* Slide 4 - NEUE SELBSTERKENNTNIS */
        #slide4-hotspot1 { top: 20%; left: 25%; }
        #slide4-hotspot2 { top: 35%; left: 45%; }
        #slide4-hotspot3 { top: 50%; left: 65%; }
        #slide4-hotspot4 { top: 65%; left: 30%; }
        #slide4-hotspot5 { top: 80%; left: 50%; }
        
        /* Slide 5 - NEUE BEZIEHUNG ZUR WELT */
        #slide5-hotspot1 { top: 40%; left: 30%; }
        #slide5-hotspot2 { top: 60%; left: 70%; }
        
        /* Slide 6 - NEUE MUSIK */
        #slide6-hotspot1 { top: 35%; left: 40%; }
        #slide6-hotspot2 { top: 65%; left: 60%; }
        
        /* Slide 7 - NEUE KLEIDUNG */
        #slide7-hotspot1 { top: 50%; left: 50%; }
        
        /* Overlay für besseren Kontrast */
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.4));
            z-index: 2;
            pointer-events: none;
        }
        
        /* Dark mode */
        @media (prefers-color-scheme: dark) {
            :root {
                --background: #121212;
                --text-color: #e0e0e0;
                --light-text: #aaaaaa;
            }
            
            .header, .footer {
                background-color: rgba(18, 18, 18, 0.95);
            }
            
            .modal-content {
                background-color: #1e1e1e;
            }
        }
        
        @media (max-width: 768px) {
            .modal-content {
                width: 85%;
                max-width: 320px;
                padding: 30px 20px;
            }
            
            .hotspot {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            
            .footer {
                height: auto;
                padding: 15px;
                font-size: 12px;
            }
            
            .carousel-arrow {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }
            
            .carousel-dots {
                bottom: 15px;
            }
            
            .carousel-dot {
                width: 8px;
                height: 8px;
            }
            
            .category-title {
                font-size: 28px;
                padding: 15px 25px;
            }
            
            h2 {
                font-size: 22px;
            }
            
            p {
                font-size: 14px;
            }
            
            .header {
                font-size: 18px;
                height: 50px;
            }
            
            .main-image-container {
                margin-top: 50px;
                height: calc(100vh - 100px);
            }
        }
        
        /* Animationen für die Hotspots */
        .hotspot {
            animation: fadeInScale 0.5s ease-out backwards;
        }
        
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.5);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .carousel-slide.active .hotspot:nth-of-type(1) { animation-delay: 0.3s; }
        .carousel-slide.active .hotspot:nth-of-type(2) { animation-delay: 0.5s; }
        .carousel-slide.active .hotspot:nth-of-type(3) { animation-delay: 0.7s; }
        .carousel-slide.active .hotspot:nth-of-type(4) { animation-delay: 0.9s; }
        .carousel-slide.active .hotspot:nth-of-type(5) { animation-delay: 1.1s; }
    </style>
</head>
<body>
    <header class="header">
        Jana Sophie Breitmar
    </header>
    
    <div class="main-image-container">
        <div class="carousel-container">
            <!-- Carousel Slides mit Hotspots und Titeln -->
            
            <!-- Slide 1: NEUE ZWISCHENMENSCHLICHKEIT -->
            <div class="carousel-slide active">
                <div class="image-overlay"></div>
                <img src="kommunikation.png" alt="Neue Zwischenmenschlichkeit" class="main-image">
                <div class="category-title">NEUE ZWISCHENMENSCHLICHKEIT</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide1-hotspot1" data-modal="modal1-1">+</div>
                <div class="hotspot" id="slide1-hotspot2" data-modal="modal1-2">+</div>
                <div class="hotspot" id="slide1-hotspot3" data-modal="modal1-3">+</div>
            </div>
            
            <!-- Slide 2: NEUE ARBEITSWELT -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="arbeitswelt.png" alt="Neue Arbeitswelt" class="main-image">
                <div class="category-title">NEUE ARBEITSWELT</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide2-hotspot1" data-modal="modal2-1">+</div>
                <div class="hotspot" id="slide2-hotspot2" data-modal="modal2-2">+</div>
                <div class="hotspot" id="slide2-hotspot3" data-modal="modal2-3">+</div>
            </div>
            
            <!-- Weitere Slides folgen dem gleichen Muster... -->
            
            <!-- Slide 3: NEUE KINDERBEZIEHUNG -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="kinderbeziehung.png" alt="Neue Kinderbeziehung" class="main-image">
                <div class="category-title">NEUE KINDERBEZIEHUNG</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide3-hotspot1" data-modal="modal3-1">+</div>
                <div class="hotspot" id="slide3-hotspot2" data-modal="modal3-2">+</div>
            </div>
            
            <!-- Slide 4: NEUE SELBSTERKENNTNIS -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="selbsterkenntnis.png" alt="Neue Selbsterkenntnis" class="main-image">
                <div class="category-title">NEUE SELBSTERKENNTNIS</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide4-hotspot1" data-modal="modal4-1">+</div>
                <div class="hotspot" id="slide4-hotspot2" data-modal="modal4-2">+</div>
                <div class="hotspot" id="slide4-hotspot3" data-modal="modal4-3">+</div>
                <div class="hotspot" id="slide4-hotspot4" data-modal="modal4-4">+</div>
                <div class="hotspot" id="slide4-hotspot5" data-modal="modal4-5">+</div>
            </div>
            
            <!-- Slide 5: NEUE BEZIEHUNG ZUR WELT -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="welt.png" alt="Neue Beziehung zur Welt" class="main-image">
                <div class="category-title">NEUE BEZIEHUNG ZUR WELT</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide5-hotspot1" data-modal="modal5-1">+</div>
                <div class="hotspot" id="slide5-hotspot2" data-modal="modal5-2">+</div>
            </div>
            
            <!-- Slide 6: NEUE MUSIK -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="musik.png" alt="Neue Musik" class="main-image">
                <div class="category-title">NEUE MUSIK</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide6-hotspot1" data-modal="modal6-1">+</div>
                <div class="hotspot" id="slide6-hotspot2" data-modal="modal6-2">+</div>
            </div>
            
            <!-- Slide 7: NEUE KLEIDUNG -->
            <div class="carousel-slide">
                <div class="image-overlay"></div>
                <img src="kleidung.png" alt="Neue Kleidung" class="main-image">
                <div class="category-title">NEUE KLEIDUNG</div>
                
                <!-- Hotspots -->
                <div class="hotspot" id="slide7-hotspot1" data-modal="modal7-1">+</div>
            </div>
            
            <!-- Carousel Navigation Arrows -->
            <div class="carousel-arrow prev-arrow">&#10094;</div>
            <div class="carousel-arrow next-arrow">&#10095;</div>
            
            <!-- Carousel Dots -->
            <div class="carousel-dots">
                <div class="carousel-dot active" data-slide="0"></div>
                <div class="carousel-dot" data-slide="1"></div>
                <div class="carousel-dot" data-slide="2"></div>
                <div class="carousel-dot" data-slide="3"></div>
                <div class="carousel-dot" data-slide="4"></div>
                <div class="carousel-dot" data-slide="5"></div>
                <div class="carousel-dot" data-slide="6"></div>
            </div>
        </div>
    </div>
    
    <footer class="footer">
        &copy; 2025 Jana Sophie Breitmar | <a href="#">Impressum</a>
    </footer>
    
    <!-- Modals für alle Kategorien -->
    
    <!-- NEUE ZWISCHENMENSCHLICHKEIT Modals -->
    <div class="modal" id="modal1-1">
        <div class="modal-content">
            <button class="close-modal">&times;</button>
            <h2>Kompatibilität</h2>
            <p>Entdecke die wahre Kompatibilität mit deinem Partner! Unser exklusives Kartenset enthält tiefgründige Fragen, die über oberflächliches Dating hinausgehen und dir zeigen, ob ihr wirklich zueinander passt. Diese sorgfältig kuratierten Fragen bringen verborgene Werte, Erwartungen und Lebensziele ans Licht – bevor du emotional zu stark investiert bist.</p>
            <p>Spare dir Jahre frustrierender Beziehungen und finde heraus, ob ihr wirklich füreinander bestimmt seid.</p>
            <a href="#" class="cta-button">Kartenset entdecken</a>
        </div>
    </div>
    
    <!-- Weitere Modals folgen dem gleichen Muster... -->
    
    <script>
        // Carousel functionality
        const slides = document.querySelectorAll('.carousel-slide');
        const prevArrow = document.querySelector('.prev-arrow');
        const nextArrow = document.querySelector('.next-arrow');
        let currentSlide = 0;
        
        function showSlide(index) {
            // Hide all hotspots first
            document.querySelectorAll('.hotspot').forEach(hotspot => {
                hotspot.style.opacity = '0';
                hotspot.style.animation = 'none';
            });
            
            // Update slides
            slides.forEach(slide => slide.classList.remove('active'));
            
            if (index < 0) {
                currentSlide = slides.length - 1;
            } else if (index >= slides.length) {
                currentSlide = 0;
            } else {
                currentSlide = index;
            }
            
            slides[currentSlide].classList.add('active');
            
            // Reset animation for the current slide hotspots
            setTimeout(() => {
                slides[currentSlide].querySelectorAll('.hotspot').forEach((hotspot, i) => {
                    hotspot.style.opacity = '1';
                    hotspot.style.animation = `fadeInScale 0.5s ease-out ${0.3 + (i * 0.2)}s backwards`;
                });
            }, 300);
            
            // Update dots
            document.querySelectorAll('.carousel-dot').forEach((dot, i) => {
                if (i === currentSlide) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }
        
        prevArrow.addEventListener('click', () => {
            showSlide(currentSlide - 1);
        });
        
        nextArrow.addEventListener('click', () => {
            showSlide(currentSlide + 1);
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                showSlide(currentSlide - 1);
            } else if (e.key === 'ArrowRight') {
                showSlide(currentSlide + 1);
            }
        });
        
        // Add click event for carousel dots
        document.querySelectorAll('.carousel-dot').forEach((dot, index) => {
            dot.addEventListener('click', () => {
                showSlide(index);
            });
        });
        
        // Auto-slide functionality
        let slideInterval = setInterval(() => {
            showSlide(currentSlide + 1);
        }, 8000);
        
        // Pause auto-slide on hover
        document.querySelector('.carousel-container').addEventListener('mouseenter', () => {
            clearInterval(slideInterval);
        });
        
        document.querySelector('.carousel-container').addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                showSlide(currentSlide + 1);
            }, 8000);
        });
        
        // Hotspot functionality - open modal when hotspot is clicked
        document.querySelectorAll('.hotspot').forEach(hotspot => {
            hotspot.addEventListener('click', () => {
                const modalId = hotspot.getAttribute('data-modal');
                document.getElementById(modalId).classList.add('active');
            });
        });
        
        // Close modal when close button is clicked
        document.querySelectorAll('.close-modal').forEach(button => {
            button.addEventListener('click', () => {
                button.closest('.modal').classList.remove('active');
            });
        });
        
        // Close modal when clicking outside of modal content
        document.addEventListener('click', (e) => {
            const modals = document.querySelectorAll('.modal.active');
            modals.forEach(modal => {
                const modalContent = modal.querySelector('.modal-content');
                if (!modalContent.contains(e.target) && !e.target.classList.contains('hotspot')) {
                    modal.classList.remove('active');
                }
            });
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal.active').forEach(modal => {
                    modal.classList.remove('active');
                });
            }
        });
        
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
            } else {
                header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.05)';
            }
        });
    </script>
</body>
</html>
