<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Wertekompass - Jana <PERSON></title>
    
    <!-- Meta <PERSON> -->
    <meta name="description" content="Wertekompass - Finde heraus, was dir im Leben wirklich wichtig ist. Entdecke deine Kernwerte und nutze sie als Kompass für deine Entscheidungen.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&family=Montserrat:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Hauptstyle der Website einbinden -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Wertekompass-spezifische Styles -->
    <style>
        /* Allgemeine Styles für Wertekompass */
        body {
            background-color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .main-title {
            font-family: 'Montserrat', sans-serif;
            color: #333;
            font-size: 32px;
            text-align: center;
            margin: 20px 0 40px;
            font-weight: 600;
        }
        
        .intro-text {
            font-family: 'Open Sans', sans-serif;
            color: #444;
            line-height: 1.7;
            max-width: 800px;
            margin: 0 auto 40px;
            text-align: center;
            font-size: 17px;
        }
        
        /* Neue Hauptnavigation mit Pfeilen */
        .main-sections-nav {
            position: sticky;
            top: 0;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            z-index: 100;
            padding: 15px 0;
            margin-bottom: 30px;
        }
        
        .main-section-btn {
            border: none;
            background-color: #f0f0f0;
            padding: 12px 20px;
            border-radius: 5px;
            font-family: 'Montserrat', sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .main-section-btn:hover {
            background-color: #e0e0e0;
        }
        
        .main-section-btn.active {
            background-color: #7c6cd8;
            color: white;
        }
        
        /* Pfeile zwischen den Hauptabschnitten */
        .section-arrow {
            width: 40px;
            height: 30px;
            margin: 0 10px;
            position: relative;
        }
        
        .section-arrow::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background-color: #7c6cd8;
            transform: translateY(-50%);
        }
        
        .section-arrow::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            width: 12px;
            height: 12px;
            border-top: 4px solid #7c6cd8;
            border-right: 4px solid #7c6cd8;
            transform: translateY(-50%) rotate(45deg);
            margin-right: 3px;
        }
        
        /* Zeitlinien-Navigation */
        .timeline-layout {
            display: flex;
            gap: 30px;
            margin-top: 20px;
            flex: 1;
        }
        
        .steps-timeline {
            width: 220px;
            position: sticky;
            top: 100px;
            align-self: flex-start;
            max-height: calc(100vh - 150px);
            overflow-y: auto;
            padding-right: 10px;
            scrollbar-width: thin;
        }
        
        .steps-timeline::-webkit-scrollbar {
            width: 5px;
        }
        
        .steps-timeline::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .steps-timeline::-webkit-scrollbar-thumb {
            background: #ccc;
        }
        
        .timeline-item {
            position: relative;
            padding: 0 0 30px 30px;
            border-left: 2px solid #e0e0e0;
        }
        
        .timeline-item:last-child {
            border-left: 2px solid transparent;
        }
        
        .timeline-item.active::before {
            content: '';
            position: absolute;
            left: -9px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #7c6cd8;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #7c6cd8;
        }
        
        .timeline-item:not(.active)::before {
            content: '';
            position: absolute;
            left: -7px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #d0d0d0;
        }
        
        .timeline-item.completed:not(.active)::before {
            background-color: #d3cfef;
            border-color: #7c6cd8;
        }
        
        .timeline-button {
            font-family: 'Open Sans', sans-serif;
            font-size: 15px;
            font-weight: 500;
            color: #555;
            background: none;
            border: none;
            padding: 0;
            text-align: left;
            cursor: pointer;
            transition: color 0.2s ease;
            width: 100%;
            display: block;
        }
        
        .timeline-button:hover {
            color: #7c6cd8;
        }
        
        .timeline-item.active .timeline-button {
            color: #7c6cd8;
            font-weight: 600;
        }
        
        .timeline-section-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 15px;
            font-weight: 600;
            color: #333;
            margin: 20px 0 15px;
            padding-left: 30px;
        }
        
        /* Inhalt-Container */
        .content-container {
            flex: 1;
            min-height: 400px;
        }
        
        /* Step Container Styling */
        .step-container {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .step-container.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .step-title {
            font-family: 'Montserrat', sans-serif;
            color: #333;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .step-description {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.7;
            margin-bottom: 30px;
            color: #444;
        }
        
        /* Werte-Liste */
        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .value-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            border: 1px solid #e0e0e0;
            background-color: #f9f9f9;
        }
        
        .value-item:hover {
            background-color: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.05);
        }
        
        .value-item.selected {
            background-color: rgba(124, 108, 216, 0.1);
            border-color: rgba(124, 108, 216, 0.4);
        }
        
        .value-item.custom {
            border-style: dashed;
        }
        
        .value-input {
            border: none;
            background: transparent;
            font-family: 'Open Sans', sans-serif;
            font-size: 15px;
            color: #444;
            width: 100%;
            outline: none;
        }
        
        .value-checkbox {
            margin-right: 10px;
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid #ccc;
            border-radius: 3px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .value-checkbox:checked {
            background-color: #7c6cd8;
            border-color: #7c6cd8;
        }
        
        .value-checkbox:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 14px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .value-label {
            font-family: 'Open Sans', sans-serif;
            font-size: 15px;
            color: #444;
            cursor: pointer;
        }
        
        /* Navigation Buttons */
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        
        .nav-btn {
            background-color: #7c6cd8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: 'Open Sans', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background-color: #6658b8;
            transform: translateY(-2px);
        }
        
        .nav-btn:disabled {
            background-color: #d0d0d0;
            cursor: not-allowed;
            transform: none;
        }
        
        .nav-btn.back {
            background-color: transparent;
            color: #7c6cd8;
            border: 2px solid #7c6cd8;
        }
        
        .nav-btn.back:hover {
            background-color: rgba(124, 108, 216, 0.1);
        }
        
        /* Gewählte Werte Anzeige */
        .selected-values-container {
            margin: 30px 0;
            padding: 20px;
            border-radius: 10px;
            background-color: rgba(124, 108, 216, 0.05);
            border: 1px solid rgba(124, 108, 216, 0.2);
        }
        
        .selected-values-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #444;
        }
        
        .selected-values-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .selected-value-tag {
            background-color: rgba(124, 108, 216, 0.2);
            color: #6658b8;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: 'Open Sans', sans-serif;
        }
        
        .remove-value {
            cursor: pointer;
            font-size: 14px;
            color: #6658b8;
            transition: all 0.2s;
        }
        
        .remove-value:hover {
            color: #ff6b6b;
        }
        
        /* Definitionsbereich */
        .value-definition {
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
        }
        
        .value-definition-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #7c6cd8;
        }
        
        .value-definition textarea {
            width: 100%;
            min-height: 120px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Open Sans', sans-serif;
            resize: vertical;
            font-size: 15px;
            color: #444;
            margin-top: 10px;
        }
        
        /* Werte-Hotspots */
        .values-hotspot {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            background-color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        
        .hotspot-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #7c6cd8;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .edit-hotspot {
            font-size: 14px;
            color: #7c6cd8;
            cursor: pointer;
            padding: 5px 10px;
            border: 1px solid #7c6cd8;
            border-radius: 20px;
            transition: all 0.2s;
        }
        
        .edit-values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 10px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .edit-value-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px;
        }
        
        .edit-form-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .save-btn {
            background-color: #7c6cd8;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .save-btn:hover {
            background-color: #6658b8;
        }
        
        .cancel-btn {
            background-color: #f0f0f0;
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .cancel-btn:hover {
            background-color: #e0e0e0;
        }
        
        .add-value-btn {
            background-color: #7c6cd8;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }
        
        .direct-edit-form h3 {
            margin-top: 0;
            color: #7c6cd8;
            font-size: 18px;
        }
        
        /* Wertepyramide Live-Ansicht - VERBESSERT */
        .values-pyramid-container {
            position: relative;
            max-width: 800px;
            margin: 40px auto;
            padding: 30px;
            border-radius: 15px;
            background-color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .save-image-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: #7c6cd8;
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: 'Open Sans', sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .save-image-btn:hover {
            background-color: #6658b8;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(124, 108, 216, 0.3);
        }
        
        .pyramid-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .pyramid-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .pyramid-subtitle {
            font-family: 'Open Sans', sans-serif;
            font-size: 16px;
            color: #777;
        }
        
        /* Verbesserte Pyramide */
        .values-pyramid {
            position: relative;
            margin: 0 auto;
            width: 500px;
            height: 500px;
            transition: all 0.3s ease;
        }
        
        .pyramid-circle {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 4px solid #7c6cd8;
            border-radius: 50%;
            box-shadow: 0 0 30px rgba(124, 108, 216, 0.2);
        }
        
        .pyramid-triangle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 450px;
            height: 390px;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            background-color: rgba(124, 108, 216, 0.05);
            box-shadow: inset 0 0 20px rgba(124, 108, 216, 0.1);
        }
        
        .pyramid-section {
            position: absolute;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
            padding: 15px 0;
        }
        
        .pyramid-section:hover {
            background-color: rgba(124, 108, 216, 0.08);
        }
        
        .pyramid-section-1 {
            height: 25%;
            top: 0;
            border-radius: 15px 15px 0 0;
        }
        
        .pyramid-section-2 {
            height: 25%;
            top: 25%;
            border-top: 2px solid rgba(124, 108, 216, 0.5);
        }
        
        .pyramid-section-3 {
            height: 25%;
            top: 50%;
            border-top: 2px solid rgba(124, 108, 216, 0.5);
        }
        
        .pyramid-section-4 {
            height: 25%;
            top: 75%;
            border-top: 2px solid rgba(124, 108, 216, 0.5);
            border-radius: 0 0 15px 15px;
        }
        
        .pyramid-value {
            font-family: 'Montserrat', sans-serif;
            text-align: center;
            padding: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 90%;
        }
        
        .top-value {
            font-weight: 700;
            font-size: 24px;
            color: #7c6cd8;
            margin-top: 15px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .top3-values {
            font-size: 18px;
            font-weight: 600;
            margin-top: 8px;
            color: #555;
        }
        
        .top10-values {
            font-size: 16px;
            margin-top: 5px;
            color: #666;
        }
        
        .family-values {
            font-size: 14px;
            color: #777;
            margin-top: 5px;
        }
        
        .pyramid-label {
            font-family: 'Montserrat', sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #7c6cd8;
            position: absolute;
            bottom: 5px;
            text-align: center;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 3px 0;
        }
        
        .edit-pyramid-section {
            position: absolute;
            right: 15px;
            top: 15px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #7c6cd8;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10;
        }
        
        .edit-pyramid-section:hover {
            transform: scale(1.1);
            background-color: #6658b8;
        }
        
        /* Definitionen unterhalb der Pyramide */
        .values-definitions {
            margin-top: 60px;
        }
        
        .definitions-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
            text-align: center;
        }
        
        .definition-item {
            margin-bottom: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 10px;
            border-left: 4px solid #7c6cd8;
            transition: all 0.3s ease;
        }
        
        .definition-item:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transform: translateY(-2px);
        }
        
        .definition-value {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 12px;
            color: #7c6cd8;
        }
        
        .definition-text {
            font-family: 'Open Sans', sans-serif;
            color: #444;
            line-height: 1.7;
            font-size: 15px;
        }
        
        /* Responsives Design - Komplett überarbeitet für mobile Geräte */
        @media (max-width: 768px) {
            body {
                font-size: 14px;
            }
            
            .container {
                padding: 10px 12px;
                width: 100%;
                max-width: 100%;
            }
            
            /* Haupt-Navigation auf Mobil - komplett überarbeitet */
            .main-sections-nav {
                position: relative;
                display: flex;
                flex-direction: column;
                padding: 15px 0;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-radius: 10px;
                overflow: hidden;
            }
            
            .main-section-btn {
                width: 100%;
                padding: 12px 15px;
                margin: 0;
                border-radius: 0;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid rgba(0,0,0,0.05);
            }
            
            .main-section-btn.active {
                background-color: #7c6cd8;
                color: white;
                box-shadow: 0 3px 10px rgba(124, 108, 216, 0.2);
                position: relative;
            }
            
            .main-section-btn.active::after {
                content: '▼';
                position: absolute;
                bottom: -1px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 12px;
                color: #7c6cd8;
            }
            
            .section-arrow {
                display: none; /* Pfeile werden im mobilen Design ausgeblendet */
            }
            
            /* Struktur */
            .timeline-layout {
                flex-direction: column;
                gap: 20px;
            }
            
            /* Zeitleiste als horizontale Scrollbar - komplett überarbeitet */
            .steps-timeline {
                position: relative;
                background-color: #f9f9f9;
                width: 100%;
                display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
                margin: 0 0 15px 0;
                padding: 12px 8px;
                -webkit-overflow-scrolling: touch;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                border-radius: 10px;
                scroll-snap-type: x mandatory;
            }
            
            .steps-timeline::-webkit-scrollbar {
                display: none;
            }
            
            .timeline-section {
                flex: 0 0 auto;
                margin-right: 20px;
                scroll-snap-align: start;
            }
            
            .timeline-section-title {
                white-space: nowrap;
                margin: 0 0 10px;
                padding: 0 5px;
                font-size: 13px;
                font-weight: 700;
                color: #555;
                text-transform: uppercase;
                text-align: center;
            }
            
            .timeline-items {
                display: flex;
                gap: 8px;
                padding: 0 5px;
                scroll-snap-align: start;
            }
            
            .timeline-item {
                border-left: none;
                padding: 0;
                margin-right: 8px;
                white-space: nowrap;
                scroll-snap-align: start;
            }
            
            .timeline-item::before {
                display: none;
            }
            
            .timeline-button {
                padding: 10px 15px;
                border-radius: 50px;
                background-color: #f0f0f0;
                transition: all 0.2s ease;
                font-size: 13px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                border: none;
                min-width: 44px; /* Mindestgröße für Touch-Targets */
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .timeline-button:hover,
            .timeline-button:active {
                background-color: #e5e5e5;
                transform: translateY(-1px);
            }
            
            .timeline-item.active .timeline-button {
                background-color: #7c6cd8;
                color: white;
                box-shadow: 0 3px 8px rgba(124, 108, 216, 0.3);
            }
            
            .timeline-item.completed:not(.active) .timeline-button {
                background-color: #d3cfef;
                color: #6658b8;
            }
            
            /* Überschriften und Schrittformatierung - komplett überarbeitet */
            .main-title {
                font-size: 22px;
                margin: 20px 0;
                text-align: center;
                color: #333;
                line-height: 1.3;
            }
            
            .intro-text {
                font-size: 15px;
                margin-bottom: 25px;
                line-height: 1.6;
                color: #555;
                text-align: center;
                padding: 0 5px;
            }
            
            /* Verbesserte Schrittdarstellung */
            .step-container {
                background-color: #fff;
                border-radius: 12px;
                padding: 15px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
            
            .step-title {
                font-size: 18px;
                margin: 0 0 20px;
                text-align: center;
                padding: 10px 5px;
                border-bottom: 2px solid #7c6cd8;
                color: #7c6cd8;
                font-weight: 700;
                position: relative;
                line-height: 1.3;
            }
            
            .step-title::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 2px;
                background-color: #7c6cd8;
            }
            
            .step-description {
                font-size: 14px;
                line-height: 1.7;
                color: #444;
                margin-bottom: 25px;
                background-color: #f9f9f9;
                padding: 15px;
                border-radius: 8px;
                border-left: 3px solid #e0e0e0;
            }
            
            .step-description strong {
                color: #7c6cd8;
                font-weight: 600;
            }
            
            .step-description br {
                content: "";
                display: block;
                margin-top: 10px;
            }
            
            /* Pyramide komplett neu angepasst */
            .values-pyramid-container {
                padding: 20px 10px;
                margin: 20px auto;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                border-radius: 15px;
                position: relative;
            }
            
            .pyramid-header {
                margin-bottom: 20px;
            }
            
            .pyramid-title {
                font-size: 20px;
                margin-bottom: 10px;
            }
            
            .values-pyramid {
                width: 100%; /* Relative Breite statt fixer Größe */
                max-width: 300px;
                height: auto;
                aspect-ratio: 1/1; /* Quadratisches Verhältnis beibehalten */
                margin: 0 auto;
                transform: scale(0.9); /* Leicht kleiner für bessere mobile Darstellung */
            }
            
            .pyramid-circle {
                border-width: 3px;
            }
            
            .pyramid-triangle {
                width: 85%;
                height: 75%;
            }
            
            .pyramid-section {
                padding: 12px 0;
            }
            
            .save-image-btn {
                top: 15px;
                right: 15px;
                padding: 8px 12px;
                font-size: 13px;
                border-radius: 30px;
                white-space: nowrap;
            }
            
            .top-value {
                font-size: 18px;
                margin-top: 8px;
                font-weight: 700;
            }
            
            .top3-values {
                font-size: 14px;
                margin-top: 5px;
                padding: 0 15px;
            }
            
            .top10-values {
                font-size: 12px;
                margin-top: 3px;
                padding: 0 15px;
            }
            
            .family-values {
                font-size: 10px;
                margin-top: 3px;
                padding: 0 15px;
            }
            
            .pyramid-label {
                font-size: 10px;
                padding: 3px 0;
                background-color: rgba(255, 255, 255, 0.8);
            }
            
            .edit-pyramid-section {
                width: 28px;
                height: 28px;
                right: 5px;
                top: 5px;
                font-size: 14px;
                z-index: 5;
            }
            
            /* Werte-Grid und Auswahlelemente - Verbesserte mobile Darstellung */
            .values-grid {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                gap: 10px;
                margin: 25px 0;
                padding: 0 5px;
            }
            
            .value-item {
                width: calc(50% - 5px); /* 2 Spalten für mobile Ansicht mit weniger Gap */
                min-height: 48px; /* Größere Touch-Target-Größe */
                padding: 10px 12px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                background-color: white;
                box-shadow: 0 2px 6px rgba(0,0,0,0.08);
                transition: all 0.2s ease;
                border: 1px solid #e8e8e8;
                position: relative;
                overflow: hidden;
            }
            
            .value-item::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 3px;
                background-color: #7c6cd8;
                opacity: 0;
                transition: opacity 0.2s ease;
            }
            
            .value-item:hover, 
            .value-item:active {
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(0,0,0,0.12);
                border-color: #d8d8d8;
            }
            
            .value-item.selected {
                background-color: rgba(124, 108, 216, 0.08);
                border-color: #7c6cd8;
                box-shadow: 0 3px 8px rgba(124, 108, 216, 0.1);
            }
            
            .value-item.selected::before {
                opacity: 1;
            }
            
            .value-checkbox {
                width: 22px;
                height: 22px;
                margin-right: 10px;
                flex-shrink: 0;
                border: 2px solid #ccc;
                position: relative;
                transition: all 0.2s ease;
            }
            
            .value-item.selected .value-checkbox {
                border-color: #7c6cd8;
            }
            
            .value-label {
                font-size: 14px;
                line-height: 1.4;
                flex-grow: 1;
                white-space: normal; /* Erlaube Umbrüche */
                word-wrap: break-word;
                color: #333;
                font-weight: 500;
            }
            
            .value-item.selected .value-label {
                color: #000;
                font-weight: 600;
            }
            
            .value-input {
                font-size: 14px;
                width: 100%;
                padding: 5px 0;
                border: none;
                background: transparent;
                font-family: 'Open Sans', sans-serif;
                color: #333;
            }
            
            /* Custom-Value-Items (mit Eingabefeld) */
            .value-item.custom {
                border-style: dashed;
                background-color: #f9f9f9;
            }
            
            /* Navigation Buttons - Komplett überarbeitet */
            .navigation-buttons {
                flex-direction: column-reverse;
                gap: 12px;
                margin: 35px 0 20px;
                padding: 0 5px;
            }
            
            .nav-btn {
                width: 100%;
                padding: 16px;
                font-size: 16px;
                border-radius: 50px; /* Runde Buttons für bessere mobile Ästhetik */
                text-align: center;
                box-shadow: 0 4px 12px rgba(0,0,0,0.12);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                font-family: 'Montserrat', sans-serif;
                font-weight: 600;
                letter-spacing: 0.5px;
                min-height: 55px; /* Ausreichende Tap-Ziel-Größe */
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .nav-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .nav-btn:hover::before,
            .nav-btn:active::before {
                opacity: 1;
            }
            
            .nav-btn:hover,
            .nav-btn:active {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            }
            
            .nav-btn.next {
                background-color: #7c6cd8;
                color: white;
                font-weight: 700;
            }
            
            .nav-btn.next::after {
                content: ' →';
                margin-left: 5px;
                transition: transform 0.2s ease;
                display: inline-block;
            }
            
            .nav-btn.next:hover::after {
                transform: translateX(3px);
            }
            
            .nav-btn.back {
                background-color: white;
                color: #7c6cd8;
                border: 2px solid #7c6cd8;
            }
            
            .nav-btn.back::before {
                content: '← ';
                margin-right: 5px;
                transition: transform 0.2s ease;
                display: inline-block;
            }
            
            .nav-btn.back:hover::before {
                transform: translateX(-3px);
            }
            
            .nav-btn:disabled {
                background-color: #e0e0e0;
                color: #999;
                border-color: #e0e0e0;
                box-shadow: none;
                cursor: not-allowed;
                transform: none;
            }
            
            .nav-btn:disabled::after,
            .nav-btn:disabled::before {
                content: none;
            }
            
            /* Auswahlzähler verbessert */
            .selected-values-counter {
                text-align: center;
                margin: 20px 0;
                font-size: 14px;
                font-weight: 600;
                padding: 10px 15px;
                background-color: #f9f9f9;
                border-radius: 50px;
                color: #555;
                box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                transition: all 0.3s ease;
            }
            
            /* Container und andere Elemente - Optimierte mobile Darstellung */
            .selected-values-container {
                padding: 18px 15px;
                margin: 25px 0;
                border-radius: 12px;
                background-color: rgba(124, 108, 216, 0.05);
                border: 1px solid rgba(124, 108, 216, 0.15);
                box-shadow: 0 2px 8px rgba(124, 108, 216, 0.05);
            }
            
            .selected-values-title {
                font-size: 16px;
                margin-bottom: 15px;
                text-align: center;
                color: #444;
                font-weight: 600;
                position: relative;
                padding-bottom: 10px;
            }
            
            .selected-values-title::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 2px;
                background-color: rgba(124, 108, 216, 0.3);
            }
            
            .selected-values-list {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 10px;
                padding: 5px;
            }
            
            .selected-value-tag {
                font-size: 13px;
                padding: 9px 15px;
                border-radius: 50px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                background-color: rgba(124, 108, 216, 0.12);
                color: #333;
                font-weight: 500;
                transition: all 0.2s ease;
                border: 1px solid rgba(124, 108, 216, 0.2);
            }
            
            .selected-value-tag:hover {
                background-color: rgba(124, 108, 216, 0.18);
                transform: translateY(-1px);
            }
            
            /* Definition Bereiche */
            .values-definitions {
                margin-top: 35px;
                background-color: #f9f9f9;
                padding: 20px 15px;
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            }
            
            .definitions-title {
                font-size: 18px;
                margin-bottom: 25px;
                text-align: center;
                color: #333;
                font-weight: 600;
                position: relative;
                padding-bottom: 12px;
            }
            
            .definitions-title::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 50px;
                height: 2px;
                background-color: #7c6cd8;
            }
            
            .definition-item {
                margin-bottom: 20px;
                padding: 15px;
                border-radius: 10px;
                background-color: white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.06);
                border-left: 3px solid #7c6cd8;
            }
            
            .definition-value {
                font-size: 16px;
                margin-bottom: 12px;
                text-align: center;
                color: #7c6cd8;
                font-weight: 600;
                padding-bottom: 8px;
                border-bottom: 1px solid #eee;
            }
            
            .definition-text {
                font-size: 14px;
                line-height: 1.6;
                color: #444;
            }
            
            /* Einzelne Wertdefinition bearbeiten */
            .value-definition {
                padding: 18px 15px;
                margin-bottom: 20px;
                border-radius: 12px;
                background-color: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.08);
                border: 1px solid #eee;
            }
            
            .value-definition-title {
                font-size: 17px;
                margin-bottom: 15px;
                text-align: center;
                color: #7c6cd8;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }
            
            .value-definition p {
                margin-bottom: 12px;
                color: #444;
                font-size: 14px;
            }
            
            .value-definition textarea {
                min-height: 120px;
                padding: 15px;
                font-size: 14px;
                border-radius: 8px;
                width: 100%;
                box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
                border: 1px solid #ddd;
                font-family: 'Open Sans', sans-serif;
                resize: vertical;
                transition: all 0.3s ease;
            }
            
            .value-definition textarea:focus {
                border-color: #7c6cd8;
                box-shadow: 0 0 0 2px rgba(124, 108, 216, 0.2);
                outline: none;
            }
            
            /* Hotspots und Übersichten */
            .values-hotspot {
                padding: 18px 15px;
                margin: 25px 0;
                border-radius: 12px;
                background-color: white;
                box-shadow: 0 3px 12px rgba(0,0,0,0.08);
                border: 1px solid #eee;
            }
            
            .hotspot-title {
                font-size: 16px;
                margin-bottom: 18px;
                color: #333;
                font-weight: 600;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12px;
                text-align: center;
            }
            
            .edit-hotspot {
                font-size: 13px;
                padding: 6px 12px;
                border-radius: 20px;
                background-color: rgba(124, 108, 216, 0.1);
                color: #7c6cd8;
                border: 1px solid rgba(124, 108, 216, 0.3);
                font-weight: 500;
                transition: all 0.2s ease;
            }
            
            .edit-hotspot:hover,
            .edit-hotspot:active {
                background-color: rgba(124, 108, 216, 0.15);
                transform: translateY(-1px);
            }
            
            /* Für bessere Tippbarkeit auf Mobilgeräten */
            input[type="checkbox"], 
            input[type="radio"],
            button,
            .edit-hotspot,
            .edit-pyramid-section,
            .selected-value-tag {
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(0,0,0,0);
            }
            
            /* Auswahlzähler */
            .selected-values-counter {
                text-align: center;
                margin: 15px 0;
                font-size: 14px;
                font-weight: 600;
            }
        }
        
                    /* Extra kleine Geräte - Weitere Optimierungen */
        @media (max-width: 480px) {
            body {
                font-size: 13px;
            }
            
            .container {
                padding: 10px;
            }
            
            .main-title {
                font-size: 20px;
                margin: 15px 0;
            }
            
            .intro-text {
                font-size: 14px;
                padding: 0;
            }
            
            /* Verbesserte Schritt-Container */
            .step-container {
                padding: 15px 12px;
                margin-bottom: 15px;
            }
            
            .step-title {
                font-size: 18px;
                padding: 8px 5px 12px;
            }
            
            .step-description {
                font-size: 13px;
                padding: 12px;
                margin-bottom: 20px;
                line-height: 1.6;
            }
            
            /* Werte-Grid auf 1 Spalte ändern für sehr kleine Geräte */
            .values-grid {
                gap: 8px;
                margin: 20px 0;
                padding: 0;
            }
            
            .value-item {
                width: 100%; /* Volle Breite - 1 Spalte */
                min-height: 44px;
                padding: 12px 10px;
            }
            
            .value-checkbox {
                width: 20px;
                height: 20px;
            }
            
            .value-label {
                font-size: 14px;
            }
            
            /* Timeline-Buttons für bessere Tippbarkeit */
            .timeline-button {
                padding: 10px 12px;
                font-size: 12px;
            }
            
            /* Hauptnavigationsbuttons */
            .main-section-btn {
                padding: 10px;
                font-size: 13px;
            }
            
            /* Pyramide für sehr kleine Geräte optimieren */
            .values-pyramid {
                transform: scale(0.9);
                max-width: 260px;
            }
            
            .values-pyramid-container {
                padding: 15px 10px 20px;
            }
            
            .pyramid-header {
                margin-bottom: 15px;
            }
            
            .pyramid-title {
                font-size: 18px;
            }
            
            .top-value {
                font-size: 16px;
            }
            
            .top3-values {
                font-size: 13px;
            }
            
            .top10-values {
                font-size: 11px;
            }
            
            .family-values {
                font-size: 9px;
            }
            
            /* Definitionen und Hotspots */
            .values-hotspot,
            .selected-values-container,
            .value-definition {
                padding: 15px 12px;
                margin: 20px 0;
            }
            
            .selected-values-title, 
            .hotspot-title,
            .value-definition-title {
                font-size: 15px;
                margin-bottom: 12px;
            }
            
            .selected-value-tag {
                font-size: 12px;
                padding: 8px 12px;
            }
            
            /* Navigation Buttons für kleinere Displays */
            .nav-btn {
                padding: 14px;
                font-size: 15px;
                min-height: 50px;
            }
            
            .selected-values-counter {
                font-size: 13px;
                padding: 8px 12px;
                margin: 15px 0;
            }
        }
        
        /* Sehr kleine Geräte (unter 360px) - Weitere Optimierungen */
        @media (max-width: 360px) {
            body {
                font-size: 12px;
            }
            
            .container {
                padding: 8px;
            }
            
            .main-title {
                font-size: 18px;
                margin: 12px 0;
            }
            
            .intro-text {
                font-size: 13px;
                margin-bottom: 20px;
            }
            
            /* Schritt-Container */
            .step-container {
                padding: 12px 10px;
            }
            
            .step-title {
                font-size: 16px;
                padding: 8px 0 10px;
            }
            
            .step-description {
                font-size: 12px;
                padding: 10px;
                margin-bottom: 15px;
            }
            
            /* Werte-Grid */
            .values-grid {
                gap: 6px;
                margin: 15px 0;
            }
            
            .value-item {
                padding: 10px 8px;
                min-height: 40px;
            }
            
            .value-checkbox {
                width: 18px;
                height: 18px;
                margin-right: 6px;
            }
            
            .value-label {
                font-size: 13px;
            }
            
            /* Zeitleiste */
            .steps-timeline {
                padding: 8px 5px;
            }
            
            .timeline-button {
                font-size: 11px;
                padding: 8px 10px;
                min-width: 36px;
                min-height: 36px;
            }
            
            /* Pyramide */
            .values-pyramid {
                transform: scale(0.85);
                max-width: 240px;
            }
            
            .pyramid-title {
                font-size: 16px;
            }
            
            .top-value {
                font-size: 14px;
            }
            
            .top3-values {
                font-size: 12px;
            }
            
            .top10-values {
                font-size: 10px;
            }
            
            .family-values {
                font-size: 8px;
            }
            
            .values-pyramid-container {
                padding: 12px 5px 15px;
            }
            
            .edit-pyramid-section {
                width: 24px;
                height: 24px;
            }
            
            .save-image-btn {
                font-size: 11px;
                padding: 5px 8px;
                top: 10px;
                right: 10px;
            }
            
            /* Container-Elemente */
            .values-hotspot,
            .selected-values-container,
            .value-definition {
                padding: 12px 10px;
                margin: 15px 0;
            }
            
            .selected-values-title, 
            .hotspot-title,
            .value-definition-title {
                font-size: 14px;
                margin-bottom: 10px;
            }
            
            .value-definition textarea {
                min-height: 100px;
                padding: 10px;
            }
            
            /* Navigation */
            .navigation-buttons {
                gap: 8px;
                margin: 25px 0 15px;
            }
            
            .nav-btn {
                padding: 12px;
                font-size: 14px;
                min-height: 44px;
            }
        }
    </style>
</head>

<body>
    <header class="header">
        <a href="index.html" class="header-link">Jana Sophie Breitmar</a>
    </header>

    <div class="container">
        <h1 class="main-title">Wertekompass</h1>
        
        <p class="intro-text">
            Wenn du weißt, was dir im Leben wirklich wertvoll und wichtig ist, kannst du dieses Wissen als Kompass nutzen, um bei Entscheidungen für dich die beste Richtung einzuschlagen.<br><br>
            Ich begleite dich jetzt durch einen Prozess, um dir über deine Lebenswerte klarzuwerden.
        </p>
        
        <!-- Hauptnavigation für die drei Hauptabschnitte mit Pfeilen -->
        <div class="main-sections-nav">
            <button class="main-section-btn active" data-section="erkunden">WERTE ERKUNDEN</button>
            <div class="section-arrow"></div>
            <button class="main-section-btn" data-section="feinabstimmung">FEINABSTIMMUNG</button>
            <div class="section-arrow"></div>
            <button class="main-section-btn" data-section="leben">WERTE LEBEN</button>
        </div>
        
        <!-- Zeitlinien-Layout mit Schritten und Inhalten -->
        <div class="timeline-layout">
            <!-- Zeitlinien-Navigation -->
            <div class="steps-timeline">
                <!-- Werte Erkunden -->
                <div class="timeline-section" data-section="erkunden">
                    <h3 class="timeline-section-title">WERTE ERKUNDEN</h3>
                    <div class="timeline-items">
                        <div class="timeline-item active" data-step="1">
                            <button class="timeline-button" data-step="1">1. Grobauswahl</button>
                        </div>
                        <div class="timeline-item" data-step="2">
                            <button class="timeline-button" data-step="2">2. Familienwerte</button>
                        </div>
                        <div class="timeline-item" data-step="3">
                            <button class="timeline-button" data-step="3">3. Familienwerte unter der Lupe</button>
                        </div>
                        <div class="timeline-item" data-step="4">
                            <button class="timeline-button" data-step="4">4. Meine Top 10</button>
                        </div>
                        <div class="timeline-item" data-step="5">
                            <button class="timeline-button" data-step="5">5. Meine Top 3</button>
                        </div>
                        <div class="timeline-item" data-step="6">
                            <button class="timeline-button" data-step="6">6. Mein Top Wert</button>
                        </div>
                        <div class="timeline-item" data-step="7">
                            <button class="timeline-button" data-step="7">7. Übersicht und Anpassung</button>
                        </div>
                    </div>
                </div>
                
                <!-- Feinabstimmung -->
                <div class="timeline-section" data-section="feinabstimmung" style="display:none;">
                    <h3 class="timeline-section-title">FEINABSTIMMUNG</h3>
                    <div class="timeline-items">
                        <div class="timeline-item" data-step="8">
                            <button class="timeline-button" data-step="8">8. Bedeutung der Werte</button>
                        </div>
                        <div class="timeline-item" data-step="9">
                            <button class="timeline-button" data-step="9">9. Übersicht und Anpassung</button>
                        </div>
                    </div>
                </div>
                
                <!-- Werte Leben -->
                <div class="timeline-section" data-section="leben" style="display:none;">
                    <h3 class="timeline-section-title">WERTE LEBEN</h3>
                    <div class="timeline-items">
                        <div class="timeline-item" data-step="10">
                            <button class="timeline-button" data-step="10">10. Bestandsaufnahme</button>
                        </div>
                        <div class="timeline-item" data-step="11">
                            <button class="timeline-button" data-step="11">11. Richtungsentscheidungen</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Inhaltsbereich -->
            <div class="content-container">
                <!-- Schritt 1: Grobauswahl -->
                <div id="step1" class="step-container active">
                    <h2 class="step-title">Schritt 1: Grobauswahl</h2>
                    <p class="step-description">
                        Markiere alle Begriffe, die mit dir in Resonanz gehen. Du erkennst eine Resonanz daran, dass diese Begriffe Gefühle in dir auslösen. Es entsteht wie so ein kleiner Sog hin zu bestimmten Begriffen. Das können Dinge sein, die dir im Leben fehlen und von denen du dir mehr in deinem Leben wünschst oder Begriffe, die deine Persönlichkeit ausmachen und mit denen du identifiziert bist.
                        <br><br>
                        <strong>Wähle mindestens 10 Begriffe.</strong> Wenn du dir noch unsicher bist, kannst du aber auch deutlich mehr auswählen. Wir werden das in den nächsten Schritten noch zusammen konkretisieren.
                    </p>
                    
                    <div class="values-grid" id="step1-values">
                        <!-- Diese Liste wird mit JavaScript gefüllt -->
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back" disabled>Zurück</button>
                        <button class="nav-btn next" id="step1-next">Weiter zu Schritt 2</button>
                    </div>
                </div>
                
                <!-- Schritt 2: Familienwerte -->
                <div id="step2" class="step-container">
                    <h2 class="step-title">Schritt 2: Familienwerte</h2>
                    <p class="step-description">
                        Nun analysieren wir deine Familienwerte. Schaue diese Liste noch einmal durch und markiere diesmal alle Begriffe, von denen du glaubst, dass deine Familie, insbesondere deine Eltern, sie ausgewählt hätten.
                    </p>
                    
                    <div class="selected-values-container">
                        <h3 class="selected-values-title">Deine ausgewählten Werte:</h3>
                        <div class="values-grid" id="step2-values">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step2-next">Weiter zu Schritt 3</button>
                    </div>
                </div>
                
                <!-- Schritt 3: Familienwerte unter der Lupe -->
                <div id="step3" class="step-container">
                    <h2 class="step-title">Schritt 3: Familienwerte unter der Lupe</h2>
                    <p class="step-description">
                        Schritt 3 wird jetzt ein Abgleich deiner Werte mit deinen Familienwerten. Unten siehst du eine Auflistung der Familienwerte sowie der Werte, die du in Übereinstimmung mit deiner Familie ausgewählt hast.
                        <br><br>
                        Diese Werte hast du durch das Aufwachsen in deinem sozialen Umfeld entwickelt, da sie deiner Familie wichtig sind. Deine Familienwerte können im Einklang mit deinem wahren Selbst sein oder dich von deinem Weg abbringen und irritieren.
                        <br><br>
                        Familienwerte sind oft seit Generationen unbewusst weitergegeben. Deswegen handelt es sich bei Familienwerten oft um "weg-von"-Werten statt um "hin-zu"-Werte. Ehrlichkeit kann zum Beispiel als typischer "weg-von"-Wert weitergegeben werden, wenn ein Familienmitglied mal sehr schmerzlich angelogen wurde und sich entschieden hat, so etwas nicht noch einmal zu tolerieren. Da wir deine höchste Kompass-Richtung finden wollen, sammeln wir für dich vor allem die "hin-zu"-Werte.
                        <br><br>
                        Die "weg-von"-Werte darfst du als selbstverständlich ansehen. Also angenommen, Ehrlichkeit wäre selbstverständlich in deinem Leben, würde Ehrlichkeit dein Herz dann noch höher springen lassen?
                        <br><br>
                        Schau die Liste noch einmal durch und entscheide ganz bewusst: Welche Werte möchtest DU für deinen Lebensweg bewusst übernehmen und behalten? Wähle nur die Werte aus, die ein warmes, positives Gefühl in dir auslösen.
                        <br><br>
                        Du kannst dich auch dazu entscheiden, Begriffe zu übernehmen, aber sie für dich neu und anders zu interpretieren.
                    </p>
                    
                    <div class="values-hotspot">
                        <h3 class="hotspot-title">Familienwerte <span class="edit-hotspot" id="edit-familywerte">Anpassen</span></h3>
                        <div class="selected-values-list" id="family-values-list">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="values-hotspot">
                        <h3 class="hotspot-title">Übereinstimmende Werte</h3>
                        <div class="selected-values-list" id="matching-values-list">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="selected-values-container">
                        <h3 class="selected-values-title">Welche Familienwerte möchtest du wirklich übernehmen?</h3>
                        <div class="values-grid" id="step3-values">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step3-next">Weiter zu Schritt 4</button>
                    </div>
                </div>
                
                <!-- Schritt 4: Meine Top 10 -->
                <div id="step4" class="step-container">
                    <h2 class="step-title">Schritt 4: Meine Top 10</h2>
                    <p class="step-description">
                        Hier findest du jetzt alle Werte, die mit dir in Resonanz gegangen sind. Wähle die wichtigsten 10 davon aus.
                    </p>
                    
                    <div class="values-grid" id="step4-values">
                        <!-- Diese Liste wird mit JavaScript gefüllt -->
                    </div>
                    
                    <div class="selected-values-counter" id="selected-counter-step4"></div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step4-next">Weiter zu Schritt 5</button>
                    </div>
                </div>
                
                <!-- Schritt 5: Meine Top 3 -->
                <div id="step5" class="step-container">
                    <h2 class="step-title">Schritt 5: Meine Top 3</h2>
                    <p class="step-description">
                        Wenn du nur drei Begriffe auswählen darfst, welche Begriffe wählst du?
                    </p>
                    
                    <div class="values-hotspot">
                        <h3 class="hotspot-title">Meine Top 10 Werte <span class="edit-hotspot" id="edit-top10">Anpassen</span></h3>
                        <div class="selected-values-list" id="top10-values-list">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="values-grid" id="step5-values">
                        <!-- Diese Liste wird mit JavaScript gefüllt -->
                    </div>
                    
                    <div class="selected-values-counter" id="selected-counter-step5"></div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step5-next">Weiter zu Schritt 6</button>
                    </div>
                </div>
                
                <!-- Schritt 6: Mein Top Wert -->
                <div id="step6" class="step-container">
                    <h2 class="step-title">Schritt 6: Mein Top Wert</h2>
                    <p class="step-description">
                        Aus diesen drei Begriffen, welcher resoniert am meisten mit dir? Welcher löst die ruhigste Ruhe oder freudigste Freude in dir aus? Was ist dir in deinem Leben wirklich wertvoll und wichtig?
                    </p>
                    
                    <div class="values-hotspot">
                        <h3 class="hotspot-title">Meine Top 3 Werte <span class="edit-hotspot" id="edit-top3">Anpassen</span></h3>
                        <div class="selected-values-list" id="top3-values-list">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div class="values-grid" id="step6-values">
                        <!-- Diese Liste wird mit JavaScript gefüllt -->
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step6-next">Weiter zu Schritt 7</button>
                    </div>
                </div>
                
                <!-- Schritt 7: Übersicht und Anpassung -->
                <div id="step7" class="step-container">
                    <h2 class="step-title">Schritt 7: Übersicht und Anpassung</h2>
                    <p class="step-description">
                        Hier kannst du deine Wertepyramide überprüfen und bei Bedarf anpassen. Die Werte in der Pyramide repräsentieren deinen persönlichen Wertekompass, den du als Orientierung für deine Entscheidungen nutzen kannst.
                    </p>
                    
                    <div class="values-pyramid-container">
                        <button class="save-image-btn" id="save-pyramid-image-7">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                <polyline points="7 3 7 8 15 8"></polyline>
                            </svg>
                            Bild speichern
                        </button>
                        
                        <div class="pyramid-header">
                            <h3 class="pyramid-title">Meine Wertepyramide</h3>
                        </div>
                        
                        <div class="values-pyramid" id="values-pyramid-7">
                            <div class="pyramid-circle"></div>
                            <div class="pyramid-triangle">
                                <!-- Ebene 1: Top Wert -->
                                <div class="pyramid-section pyramid-section-1">
                                    <div class="pyramid-value top-value" id="pyramid-top-value"></div>
                                    <div class="pyramid-label">Top Wert</div>
                                    <div class="edit-pyramid-section" data-section="top-value">✎</div>
                                </div>
                                
                                <!-- Ebene 2: Top 3 Werte -->
                                <div class="pyramid-section pyramid-section-2">
                                    <div class="pyramid-value top3-values" id="pyramid-top3-values"></div>
                                    <div class="pyramid-label">Top 3 Werte</div>
                                    <div class="edit-pyramid-section" data-section="top3-values">✎</div>
                                </div>
                                
                                <!-- Ebene 3: Top 10 Werte -->
                                <div class="pyramid-section pyramid-section-3">
                                    <div class="pyramid-value top10-values" id="pyramid-top10-values"></div>
                                    <div class="pyramid-label">Top 10 Werte</div>
                                    <div class="edit-pyramid-section" data-section="top10-values">✎</div>
                                </div>
                                
                                <!-- Ebene 4: Familienwerte -->
                                <div class="pyramid-section pyramid-section-4">
                                    <div class="pyramid-value family-values" id="pyramid-family-values"></div>
                                    <div class="pyramid-label">Familienwerte</div>
                                    <div class="edit-pyramid-section" data-section="family-values">✎</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step7-next">Weiter zur Feinabstimmung</button>
                    </div>
                </div>
                
                <!-- Schritt 8: Bedeutung der Werte -->
                <div id="step8" class="step-container">
                    <h2 class="step-title">Schritt 8: Bedeutung der Werte</h2>
                    <p class="step-description">
                        Jeder Begriff kann auf verschiedene Weisen interpretiert werden. Deswegen ist es wichtig, dass du genau weißt, wie der Begriff für dich definiert ist. So kannst du Unklarheiten mit anderen aus dem Weg räumen.<br><br>
                        Wenn zum Beispiel jemand sagt, sein höchster Wert sei Ehrlichkeit, dann kann das bedeuten, er hat einen "Weg-von"-Wert und möchte nicht angelogen werden. Es kann auch bedeuten, dass er kein Blatt vor den Mund nimmt und sein Herz auf der Zunge trägt. Damit kann er aber zum Beispiel auch verletzende Dinge sagen oder eben nicht, je nach dem ob Sensibilität auch ein Wert von ihm ist oder nicht. Genauso kann mit Ehrlichkeit auch gemeint sein, ehrlich zu sich selbst zu sein oder auch ganz ehrlich auf sensible Art Probleme anzusprechen.<br><br>
                        Wie du siehst, gibt es hier sehr viel Interpretationsspielraum. Definiere hier deine Werte näher: Was bedeuten diese Begriffe für dich? Erinnere dich daran, dass sie ab jetzt die Wegweiser in deinem Leben sein werden. Wähle also Begriffe, die sich für dich schön anfühlen.<br><br>
                        <strong>Tipp:</strong> Oft hilft es, noch ein zusätzliches Wort hinzuzufügen, das deinen Wertebegriff näher beschreibt, also zum Beispiel "Sicherheit durch Ehrlichkeit", "Schonungslose Ehrlichkeit" oder "Feinfühlige Ehrlichkeit". Du kannst die Begriffe hier jetzt nach Belieben anpassen. Klicke auf das Bleistift-Symbol neben dem Wert, um den Begriff zu ändern.
                    </p>
                    
                    <div class="values-hotspot">
                        <h3 class="hotspot-title">Mein höchster Wert <span class="edit-hotspot" id="edit-top1">Anpassen</span></h3>
                        <div class="selected-values-list" id="top1-value-list">
                            <!-- Diese Liste wird mit JavaScript gefüllt -->
                        </div>
                    </div>
                    
                    <div id="definition-container">
                        <!-- Diese Liste wird mit JavaScript gefüllt -->
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step8-next">Weiter zu Schritt 9</button>
                    </div>
                </div>
                
                <!-- Schritt 9: Übersicht und Anpassung (mit Definitionen) -->
                <div id="step9" class="step-container">
                    <h2 class="step-title">Schritt 9: Übersicht und Anpassung</h2>
                    <p class="step-description">
                        Hier siehst du die finale Version deiner Wertepyramide mit den Definitionen deiner wichtigsten Werte. Du kannst die Pyramide nochmals anpassen und als Bild speichern.
                    </p>
                    
                    <div class="values-pyramid-container">
                        <button class="save-image-btn" id="save-pyramid-image-9">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                <polyline points="7 3 7 8 15 8"></polyline>
                            </svg>
                            Bild speichern
                        </button>
                        
                        <div class="pyramid-header">
                            <h3 class="pyramid-title">Meine Wertepyramide</h3>
                        </div>
                        
                        <div class="values-pyramid" id="values-pyramid-9">
                            <div class="pyramid-circle"></div>
                            <div class="pyramid-triangle">
                                <!-- Ebene 1: Top Wert -->
                                <div class="pyramid-section pyramid-section-1">
                                    <div class="pyramid-value top-value" id="pyramid-top-value-9"></div>
                                    <div class="pyramid-label">Top Wert</div>
                                    <div class="edit-pyramid-section" data-section="top-value-9">✎</div>
                                </div>
                                
                                <!-- Ebene 2: Top 3 Werte -->
                                <div class="pyramid-section pyramid-section-2">
                                    <div class="pyramid-value top3-values" id="pyramid-top3-values-9"></div>
                                    <div class="pyramid-label">Top 3 Werte</div>
                                    <div class="edit-pyramid-section" data-section="top3-values-9">✎</div>
                                </div>
                                
                                <!-- Ebene 3: Top 10 Werte -->
                                <div class="pyramid-section pyramid-section-3">
                                    <div class="pyramid-value top10-values" id="pyramid-top10-values-9"></div>
                                    <div class="pyramid-label">Top 10 Werte</div>
                                    <div class="edit-pyramid-section" data-section="top10-values-9">✎</div>
                                </div>
                                
                                <!-- Ebene 4: Familienwerte -->
                                <div class="pyramid-section pyramid-section-4">
                                    <div class="pyramid-value family-values" id="pyramid-family-values-9"></div>
                                    <div class="pyramid-label">Familienwerte</div>
                                    <div class="edit-pyramid-section" data-section="family-values-9">✎</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Definitionen der Werte -->
                        <div class="values-definitions">
                            <h3 class="definitions-title">Was meine Werte für mich bedeuten</h3>
                            <div id="value-definitions-container">
                                <!-- Diese Liste wird mit JavaScript gefüllt -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step9-next">Weiter zu Werte Leben</button>
                    </div>
                </div>
                
                <!-- Schritt 10: Bestandsaufnahme -->
                <div id="step10" class="step-container">
                    <h2 class="step-title">Schritt 10: Bestandsaufnahme</h2>
                    <p class="step-description">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor velit vel justo ullamcorper, in ullamcorper nisi tincidunt. Suspendisse potenti. Praesent ac neque id urna convallis ultrices. 
                        <br><br>
                        Donec fringilla, lorem in eleifend elementum, libero nisi fermentum nulla, ut scelerisque justo augue a lectus. Cras id erat vel urna tincidunt tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nullam blandit, odio eu fermentum laoreet, purus tortor dapibus purus, a scelerisque lorem ipsum vel dolor.
                        <br><br>
                        Curabitur auctor, nisl ut feugiat condimentum, tortor arcu posuere ipsum, nec malesuada velit quam id metus. Quisque vitae erat vitae eros mattis pellentesque ac non lacus.
                    </p>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step10-next">Weiter zu Schritt 11</button>
                    </div>
                </div>
                
                <!-- Schritt 11: Richtungsentscheidungen -->
                <div id="step11" class="step-container">
                    <h2 class="step-title">Schritt 11: Richtungsentscheidungen</h2>
                    <p class="step-description">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce gravida nisl ut neque lacinia, vel hendrerit odio consectetur. Nulla facilisi. Aliquam erat volutpat. Suspendisse potenti. Maecenas eget velit sed ex vehicula lobortis.
                        <br><br>
                        Praesent fermentum mauris vel justo finibus, et sagittis lacus volutpat. In hac habitasse platea dictumst. Proin dapibus arcu vel ornare egestas. Sed commodo aliquet purus, ut semper justo facilisis vel. Donec pellentesque finibus dui, eget ultrices est porttitor eu.
                        <br><br>
                        Cras posuere consectetur mi at elementum. Phasellus elementum ipsum et magna lacinia, at vehicula leo maximus. Integer eu justo sit amet eros dictum viverra. Nulla nec consequat tortor. Vivamus nibh sapien, sollicitudin nec interdum id, iaculis vitae ante.
                    </p>
                    
                    <div class="navigation-buttons">
                        <button class="nav-btn back">Zurück</button>
                        <button class="nav-btn next" id="step11-next" disabled>Fertig</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="footer">
        &copy; 2025 <a href="#" id="footer-ueber-mich-link">Jana Sophie Breitmar</a> | <a href="#" id="whats-new-link">What's new?</a> | <a href="#" id="impressum-link">Impressum</a>
    </footer>

    <!-- Hauptskripte der Website einbinden -->
    <script src="cookie-banner.js"></script>
    
    <!-- PDF.js Bibliothek für PDF Generierung -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- html2canvas für Bildspeicherung -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Wertekompass-spezifisches JavaScript -->
    <script src="wertekompass.js"></script>
    
    <!-- Footer-Link-Handler für Modals -->
    <script>
    // Footer-Links in wertekompass.html abfangen
    document.addEventListener('DOMContentLoaded', function() {
        const footerUeberMichLink = document.getElementById('footer-ueber-mich-link');
        const whatsNewLink = document.getElementById('whats-new-link');
        const impressumLink = document.getElementById('impressum-link');
        
        // Event-Listener für Über-mich-Link
        if (footerUeberMichLink) {
            footerUeberMichLink.addEventListener('click', function(e) {
                e.preventDefault();
                // Zur Hauptseite navigieren mit Modal-Parameter
                window.location.href = 'index.html?modal=ueber-mich';
            });
        }
        
        // Event-Listener für What's-new-Link
        if (whatsNewLink) {
            whatsNewLink.addEventListener('click', function(e) {
                e.preventDefault();
                // Zur Hauptseite navigieren mit Modal-Parameter
                window.location.href = 'index.html?modal=whats-new';
            });
        }
        
        // Event-Listener für Impressum-Link
        if (impressumLink) {
            impressumLink.addEventListener('click', function(e) {
                e.preventDefault();
                // Zur Hauptseite navigieren mit Modal-Parameter
                window.location.href = 'index.html?modal=impressum';
            });
        }
    });
    </script>

</body>

</html>