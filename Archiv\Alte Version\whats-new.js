// whats-new.js - Optimierte Version für einen spannenden Feed

document.addEventListener('DOMContentLoaded', function() {
    // Prü<PERSON>, ob die erforderlichen Daten geladen wurden
    if (typeof modalsData === 'undefined' || typeof slidesData === 'undefined') {
        console.error('Fehler: Daten aus content.js nicht verfügbar. Bitte stellen Si<PERSON> sicher, dass content.js korrekt geladen wird.');
        return;
    }

    // Referenzen zu DOM-Elementen
    const whatsNewLink = document.getElementById('whats-new-link');
    const whatsNewModal = document.getElementById('whats-new-modal');
    const whatsNewContent = document.getElementById('whats-new-content');
    
    // Kategorien-Farbschema definieren
    const categoryColors = {
        "zwischenmenschlichkeit": "#FF7E6B", // Warmrot
        "emotionalitaet": "#7E9AFF",         // Hellblau
        "selbsterkenntnis": "#9C6BFF",       // Lila
        "arbeitswelt": "#FFC764",            // <PERSON>elb
        "kinderbeziehung": "#64DDFF",        // Türkis
        "welt": "#68C874"                   // Grün
    };
    
    // Funktion zum Extrahieren der Kategorie aus der Modal-ID
    function getCategoryFromModalId(modalId) {
        const parts = modalId.split('-');
        if (parts.length >= 2) {
            return parts[1]; // "modal-kategorie-name" -> "kategorie"
        }
        return '';
    }
    
    // Funktion zum Finden des zugehörigen Slides für ein Modal
    function findSlideForModal(modalId) {
        for (let i = 0; i < slidesData.length; i++) {
            const slide = slidesData[i];
            for (let j = 0; j < slide.hotspots.length; j++) {
                if (slide.hotspots[j].modalId === modalId) {
                    return {
                        slide: slide,
                        slideIndex: i
                    };
                }
            }
        }
        return null;
    }
    
    // Funktion zum Generieren des Inhalts für das "What's new?" Modal
    function generateWhatsNewContent() {
        // Filtern nach Artikeln
        const articles = modalsData.filter(modal => modal.isArticle);
        
        // Sortieren nach Veröffentlichungsdatum (neueste zuerst)
        const sortedArticles = articles.sort((a, b) => {
            // Datum als Array [Tag, Monat, Jahr] parsen
            const dateA = a.publishDate.split('.').map(Number);
            const dateB = b.publishDate.split('.').map(Number);
            
            // Wandle das Datum in einen vergleichbaren Wert um (Jahr * 10000 + Monat * 100 + Tag)
            const valueA = dateA[2] * 10000 + dateA[1] * 100 + dateA[0];
            const valueB = dateB[2] * 10000 + dateB[1] * 100 + dateB[0];
            
            // Absteigend sortieren (neueste zuerst)
            return valueB - valueA;
        });
        
        // Artikel nach Datum gruppieren
        const articlesByDate = {};
        
        sortedArticles.forEach(article => {
            // Datum als Schlüssel verwenden
            const key = article.publishDate;
            
            if (!articlesByDate[key]) {
                articlesByDate[key] = [];
            }
            
            articlesByDate[key].push(article);
        });
        
        // Datum-Schlüssel absteigend sortieren
        const sortedDateKeys = Object.keys(articlesByDate).sort((a, b) => {
            // Datum als Array [Tag, Monat, Jahr] parsen
            const dateA = a.split('.').map(Number);
            const dateB = b.split('.').map(Number);
            
            // Vergleichbare Werte erstellen (Jahr * 10000 + Monat * 100 + Tag)
            const valueA = dateA[2] * 10000 + dateA[1] * 100 + dateA[0];
            const valueB = dateB[2] * 10000 + dateB[1] * 100 + dateB[0];
            
            // Absteigend sortieren
            return valueB - valueA;
        });
        
        // Inhalt generieren - Header mit Filter-Optionen und WhatsApp-Community Hinweis
        let content = `
            <div class="whats-new-header">
                <h1 class="whats-new-title">Neueste Veröffentlichungen</h1>
                <div class="filter-controls">
                    <div class="filter-button active" data-filter="all">Alle</div>
                    <div class="filter-button" data-filter="zwischenmenschlichkeit">Beziehung</div>
                    <div class="filter-button" data-filter="emotionalitaet">Emotionalität</div>
                    <div class="filter-button" data-filter="selbsterkenntnis">Selbsterkenntnis</div>
                    <div class="filter-button" data-filter="arbeitswelt">Berufswelt</div>
                    <div class="filter-button" data-filter="kinderbeziehung">Kinder</div>
                    <div class="filter-button" data-filter="welt">Welt</div>
                </div>
            </div>
            
            <div class="whatsapp-community-container">
                <p class="whatsapp-community-text">
                    Du möchtest benachrichtigt werden, wenn es etwas Neues gibt?<br> 
                    Tritt meiner <a href="#" class="whatsapp-link">WhatsApp-Community</a> bei!
                </p>
            </div>
        `;
        
        // Timeline-Container
        content += `<div class="timeline-container">`;
        
        // Für jedes Datum
        sortedDateKeys.forEach(dateKey => {
            const articlesForDate = articlesByDate[dateKey];
            
            // Datum formatieren
            const [day, month, year] = dateKey.split('.').map(Number);
            const formattedDate = `${day}. ${getMonthName(month)} ${year}`;
            
            // Zeitabschnitt-Header mit data-date Attribut für Filterung
            content += `<div class="time-period" data-date="${dateKey}">
                <div class="time-label">${formattedDate}</div>
            </div>`;
            
            // Artikel dieses Datums
            articlesForDate.forEach((article, index) => {
                // Artikel-Metadaten
                const category = getCategoryFromModalId(article.id);
                const categoryColor = categoryColors[category] || "#7c6cd8";
                
                // Extrahiere Text für Snippet
                let plainText = article.content
                    .replace(/<[^>]*>?/gm, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                    
                // Snippet-Text mit max. 450 Zeichen
                let snippet = plainText;
                if (snippet.length > 450) {
                    snippet = snippet.substring(0, 450) + '...';
                }
                
                // Finde das zugehörige Slide-Bild
                const slideInfo = findSlideForModal(article.id);
                let thumbnailHtml = '';
                
                if (slideInfo) {
                    thumbnailHtml = `
                        <div class="article-thumbnail">
                            <img src="${slideInfo.slide.image}" alt="${slideInfo.slide.altText}" class="thumbnail-image">
                            <div class="category-tag" style="background-color: ${categoryColor};">${getCategoryLabel(category)}</div>
                        </div>
                    `;
                }
                
                // "Neu" Label für das allerneueste Datum
                const isNewest = dateKey === sortedDateKeys[0];
                const newLabel = isNewest ? `<span class="new-label">Neu</span>` : '';
                
                // Artikel-Element erstellen - mit data-date Attribut zur Verknüpfung mit dem Zeitpunkt
                content += `
                    <div class="article-item" data-id="${article.id}" data-category="${category}" data-date="${dateKey}">
                        <div class="article-card">
                            <div class="article-header">
                                <h3 class="article-title">${article.title} ${newLabel}</h3>
                            </div>
                            <div class="article-content-wrapper">
                                ${thumbnailHtml}
                                <div class="article-content">
                                    <p class="article-snippet">${snippet}</p>
                                    <div class="article-actions">
                                        <span class="article-cta">${article.ctaText}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
        });
        
        content += `</div>`; // Ende des Timeline-Containers
        
        // "Mehr laden" Button
        content += `
            <div class="load-more-container">
                <button class="load-more-button">Mehr laden</button>
            </div>
        `;
        
        return content;
    }
    
    // Hilfsfunktion: Monatsnamen zurückgeben
    function getMonthName(monthNumber) {
        const monthNames = [
            'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
            'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
        ];
        return monthNames[monthNumber - 1] || '';
    }
    
    // Hilfsfunktion: Benutzerfreundliche Kategorie-Labels
    function getCategoryLabel(category) {
        const labels = {
            "zwischenmenschlichkeit": "Beziehung",
            "emotionalitaet": "Emotionalität",
            "selbsterkenntnis": "Selbsterkenntnis",
            "arbeitswelt": "Berufswelt",
            "kinderbeziehung": "Kinder",
            "welt": "Welt"
        };
        return labels[category] || category;
    }
    
    // CSS-Styles für das verbesserte "What's new?" Modal hinzufügen
    function addWhatsNewStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* What's new Modal Container Styling */
            .whats-new-modal-content {
                position: relative;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                max-width: 800px;
                width: 90%;
                max-height: 85vh;
                background-color: rgba(255, 255, 255, 0.98);
                padding: 0;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            
            /* Animations und Transitions */
            .modal.page-modal:not(.active) .whats-new-modal-content {
                transform: translate(-50%, -55%) scale(0.9);
                opacity: 0;
            }
            
            .modal.page-modal.active .whats-new-modal-content {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            
            /* Content-Scroll-Bereich mit verbessertem Scrolling */
            #whats-new-content {
                padding: 0;
                max-height: calc(85vh - 20px);
                overflow-y: auto;
                scroll-behavior: smooth; /* Sanftes Scrollen */
            }
            
            /* Header mit Titel und Filtern */
            .whats-new-header {
                position: sticky;
                top: 0;
                background-color: rgba(255, 255, 255, 0.98);
                padding: 20px 25px 15px;
                z-index: 11;
                border-bottom: 1px solid #eee;
            }
            
            .whats-new-title {
                font-family: 'Montserrat', sans-serif;
                font-weight: 600;
                font-size: 28px;
                margin-bottom: 15px;
                color: #333;
            }
            
            .filter-controls {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
                margin-bottom: 5px;
            }
            
            .filter-button {
                font-family: 'Open Sans', sans-serif;
                font-size: 14px;
                padding: 5px 12px;
                border-radius: 20px;
                background-color: #f0f0f0;
                color: #555;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .filter-button:hover {
                background-color: #e5e5e5;
            }
            
            .filter-button.active {
                background-color: #7c6cd8;
                color: white;
            }
            
            /* WhatsApp-Community Container - Fade-Effekt */
            .whatsapp-community-container {
                position: sticky;
                top: 115px; /* Nach dem Header */
                background-color: #7c6cd8;
                padding: 10px 25px;
                z-index: 10;
                box-shadow: 0 3px 5px rgba(0,0,0,0.1);
                margin-bottom: 15px;
                transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
                opacity: 1;
                transform: translateY(0);
            }
            
            .whatsapp-community-container.hidden {
                opacity: 0;
                transform: translateY(-10px);
                pointer-events: none;
            }
            
            .whatsapp-community-text {
                font-family: 'Open Sans', sans-serif;
                font-size: 15px;
                color: white;
                margin: 0;
                line-height: 1.5;
                text-align: center;
            }
            
            .whatsapp-link {
                color: white;
                font-weight: 600;
                text-decoration: underline;
                transition: all 0.2s ease;
            }
            
            .whatsapp-link:hover {
                color: rgba(255, 255, 255, 0.8);
            }
            
            /* Timeline-Container */
            .timeline-container {
                position: relative;
                padding: 20px 25px;
            }
            
            .timeline-container::before {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                left: 40px;
                width: 2px;
                background-color: #7c6cd8;
                opacity: 0.3;
                z-index: 1;
            }
            
            /* Zeitperioden-Markierungen */
            .time-period {
                position: relative;
                margin: 30px 0 20px 15px;
                z-index: 2;
            }
            
            .time-period:not(.visible) {
                display: none;
            }
            
            .time-label {
                font-family: 'Open Sans', sans-serif;
                font-weight: 500;
                font-size: 15px;
                background-color: #7c6cd8;
                color: white;
                display: inline-block;
                padding: 4px 14px;
                border-radius: 25px;
                margin-left: 10px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                letter-spacing: 0.2px;
            }
            
            /* Verbesserte Artikel-Styling */
            .article-item {
                position: relative;
                display: flex;
                margin-bottom: 25px;
                z-index: 2;
                opacity: 0;
                transform: translateY(10px);
                animation: fadeInUp 0.4s ease forwards;
            }
            
            .article-card {
                flex: 1;
                margin-left: 30px;
                background-color: white;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 3px 10px rgba(0,0,0,0.08);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .article-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.12);
            }
            
            .article-header {
                padding: 18px 20px 15px;
                border-bottom: 1px solid #f5f5f5;
            }
            
            .article-title {
                font-family: 'Montserrat', sans-serif;
                font-size: 20px;
                font-weight: 600;
                color: #333;
                margin: 0;
                line-height: 1.4;
            }
            
            .new-label {
                display: inline-block;
                background-color: #FF7E6B;
                color: white;
                font-size: 12px;
                padding: 3px 8px;
                border-radius: 4px;
                margin-left: 8px;
                vertical-align: middle;
                font-weight: 500;
            }
            
            /* Artikel-Inhalt */
            .article-content-wrapper {
                display: flex;
                padding: 18px 20px;
            }
            
            .article-thumbnail {
                position: relative;
                flex: 0 0 140px;
                height: 140px;
                overflow: hidden;
                border-radius: 6px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                margin-right: 18px;
            }
            
            .thumbnail-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }
            
            .article-card:hover .thumbnail-image {
                transform: scale(1.05);
            }
            
            .category-tag {
                position: absolute;
                top: 5px;
                left: 5px;
                padding: 4px 10px;
                border-radius: 4px;
                font-size: 12px;
                color: white;
                font-family: 'Open Sans', sans-serif;
                font-weight: 500;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            }
            
            .article-content {
                flex: 1;
                display: flex;
                flex-direction: column;
            }
            
            .article-snippet {
                font-family: 'Open Sans', sans-serif;
                font-size: 16px;
                color: #444;
                margin: 0 0 20px 0;
                line-height: 1.6;
                flex-grow: 1;
            }
            
            .article-actions {
                display: flex;
                justify-content: flex-end;
                margin-top: auto;
            }
            
            .article-cta {
                font-family: 'Open Sans', sans-serif;
                font-size: 16px;
                color: #7c6cd8;
                font-weight: 500;
            }
            
            /* Mehr laden Button */
            .load-more-container {
                display: flex;
                justify-content: center;
                padding: 20px 0 30px;
            }
            
            .load-more-button {
                font-family: 'Open Sans', sans-serif;
                background-color: transparent;
                border: 2px solid #7c6cd8;
                color: #7c6cd8;
                padding: 12px 25px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .load-more-button:hover {
                background-color: #7c6cd8;
                color: white;
            }
            
            /* Animationen für Elemente */
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .article-item:nth-child(1) { animation-delay: 0.05s; }
            .article-item:nth-child(2) { animation-delay: 0.1s; }
            .article-item:nth-child(3) { animation-delay: 0.15s; }
            .article-item:nth-child(4) { animation-delay: 0.2s; }
            .article-item:nth-child(5) { animation-delay: 0.25s; }
            .article-item:nth-child(6) { animation-delay: 0.3s; }
            .article-item:nth-child(7) { animation-delay: 0.35s; }
            .article-item:nth-child(8) { animation-delay: 0.4s; }
            .article-item:nth-child(9) { animation-delay: 0.45s; }
            .article-item:nth-child(10) { animation-delay: 0.5s; }
            
            /* Responsive Design */
            @media (max-width: 768px) {
                .timeline-container::before {
                    left: 25px;
                }
                
                .time-period {
                    margin-left: 0;
                }
                
                .article-content-wrapper {
                    flex-direction: column;
                }
                
                .article-thumbnail {
                    width: 100%;
                    height: 160px;
                    margin-right: 0;
                    margin-bottom: 15px;
                }
                
                .article-title {
                    font-size: 18px;
                }
                
                .article-snippet {
                    font-size: 15px;
                }
                
                .filter-controls {
                    gap: 5px;
                }
                
                .filter-button {
                    font-size: 13px;
                    padding: 4px 10px;
                }
                
                .whatsapp-community-text {
                    font-size: 14px;
                }
                
                .whatsapp-community-container {
                    top: 105px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    
    // Event-Listener für den "What's new?" Link
    whatsNewLink.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Alle anderen Modals schließen
        document.querySelectorAll('.modal').forEach(modal => modal.classList.remove('active'));
        document.querySelectorAll('.hotspot').forEach(hotspot => hotspot.classList.remove('active'));
        
        // "What's new?" Inhalt generieren und einfügen
        whatsNewContent.innerHTML = generateWhatsNewContent();
        
        // Alle Zeitperioden und Artikel zu Beginn sichtbar machen
        const allTimePeriods = whatsNewContent.querySelectorAll('.time-period');
        allTimePeriods.forEach(period => {
            period.classList.add('visible');
        });
        
        // WhatsApp Banner-Scroll-Logik
        const whatsAppBanner = whatsNewContent.querySelector('.whatsapp-community-container');
        let lastScrollPosition = 0;
        let scrollThreshold = 20; // Reduzierter Threshold für früheres Ausblenden
        
        whatsNewContent.addEventListener('scroll', function() {
            const currentScrollPosition = this.scrollTop;
            
            // Nur reagieren, wenn genug gescrollt wurde
            if (Math.abs(currentScrollPosition - lastScrollPosition) > scrollThreshold) {
                // Nach unten scrollen: Banner ausblenden
                if (currentScrollPosition > lastScrollPosition && currentScrollPosition > 50) {
                    whatsAppBanner.classList.add('hidden');
                } 
                // Nach oben scrollen: Banner einblenden
                else if (currentScrollPosition < lastScrollPosition) {
                    whatsAppBanner.classList.remove('hidden');
                }
                
                lastScrollPosition = currentScrollPosition;
            }
        });
        
        // Filterung-Logik hinzufügen
        const filterButtons = whatsNewContent.querySelectorAll('.filter-button');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Aktive Klasse entfernen
                filterButtons.forEach(btn => btn.classList.remove('active'));
                
                // Aktive Klasse für diesen Button setzen
                this.classList.add('active');
                
                // Filter anwenden
                const filter = this.getAttribute('data-filter');
                const articles = whatsNewContent.querySelectorAll('.article-item');
                const timePeriods = whatsNewContent.querySelectorAll('.time-period');
                
                // Setze alle Zeitperioden auf unsichtbar
                timePeriods.forEach(period => {
                    period.classList.remove('visible');
                });
                
                // Map für sichtbare Daten
                const visibleDates = new Set();
                
                // Filtere Artikel
                articles.forEach(article => {
                    if (filter === 'all' || article.getAttribute('data-category') === filter) {
                        article.style.display = '';
                        // Animation zurücksetzen
                        article.style.animation = 'none';
                        setTimeout(() => {
                            article.style.animation = '';
                        }, 10);
                        
                        // Markiere das Datum als sichtbar
                        visibleDates.add(article.getAttribute('data-date'));
                    } else {
                        article.style.display = 'none';
                    }
                });
                
                // Zeige nur Zeitperioden mit sichtbaren Artikeln
                timePeriods.forEach(period => {
                    if (visibleDates.has(period.getAttribute('data-date'))) {
                        period.classList.add('visible');
                    }
                });
            });
        });
        
        // "Mehr laden" Button Logik
        const loadMoreBtn = whatsNewContent.querySelector('.load-more-button');
        loadMoreBtn.addEventListener('click', function() {
            this.innerHTML = "Wird geladen...";
            setTimeout(() => {
                this.innerHTML = "Keine weiteren Veröffentlichungen verfügbar";
                this.disabled = true;
                this.style.opacity = "0.5";
                this.style.cursor = "default";
            }, 800);
        });
        
        // Modal öffnen
        whatsNewModal.classList.add('active');
    });
    
    // Event-Listener für Klicks auf Artikel hinzufügen
    document.addEventListener('click', function(e) {
        const articleItem = e.target.closest('.article-item');
        if (articleItem && whatsNewModal.classList.contains('active')) {
            e.preventDefault();
            e.stopPropagation();
            
            const modalId = articleItem.getAttribute('data-id');
            
            // "What's new?" Modal schließen
            whatsNewModal.classList.remove('active');
            
            // Finde den zugehörigen Hotspot und simuliere einen Klick
            const matchingHotspots = [];
            slidesData.forEach((slide, slideIndex) => {
                slide.hotspots.forEach(hotspot => {
                    if (hotspot.modalId === modalId) {
                        matchingHotspots.push({
                            hotspotId: hotspot.id,
                            slideIndex: slideIndex
                        });
                    }
                });
            });
            
            if (matchingHotspots.length > 0) {
                // Nehme den ersten passenden Hotspot
                const target = matchingHotspots[0];
                
                // Navigiere zum Slide
                const slides = document.querySelectorAll('.carousel-slide');
                const dots = document.querySelectorAll('.carousel-dot');
                
                // Entferne aktive Klasse von allen Slides
                slides.forEach(slide => slide.classList.remove('active'));
                
                // Setze den gewünschten Slide als aktiv
                slides[target.slideIndex].classList.add('active');
                
                // Aktualisiere auch die Navigations-Dots
                dots.forEach((dot, i) => {
                    if (i === target.slideIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
                
                // Warte kurz, dann klicke den Hotspot
                setTimeout(() => {
                    const hotspotElement = document.getElementById(target.hotspotId);
                    if (hotspotElement) {
                        hotspotElement.click();
                    }
                }, 500);
            }
        }
    });
    
    // Schließen des Modals über den Schließen-Button
    whatsNewModal.querySelector('.close-modal').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        whatsNewModal.classList.remove('active');
    });
    
    // Schließen des Modals durch Klick außerhalb des Inhalts
    whatsNewModal.addEventListener('click', function(e) {
        if (!e.target.closest('.modal-content') && whatsNewModal.classList.contains('active')) {
            whatsNewModal.classList.remove('active');
        }
    });
    
    // CSS-Styles hinzufügen
    addWhatsNewStyles();
});