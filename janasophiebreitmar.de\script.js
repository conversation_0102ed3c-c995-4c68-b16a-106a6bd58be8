// DOM-Elemente
const headerTitle = document.getElementById('header-title');
const impressumLink = document.getElementById('impressum-link');
const ueberMichLink = document.getElementById('ueber-mich-link');
const ueberMichModal = document.getElementById('ueber-mich-modal');
const impressumModal = document.getElementById('impressum-modal');
const ueberMichClose = document.getElementById('ueber-mich-close');
const impressumClose = document.getElementById('impressum-close');
const searchResultsModal = document.getElementById('search-results-modal');
const searchResultsClose = document.getElementById('search-results-close');
const enlargedImageModal = document.getElementById('enlarged-image-modal'); 
const enlargedImageClose = document.getElementById('enlarged-image-close');
const enlargedImage = document.getElementById('enlarged-image');

// Hexagon Modals
const musikHex = document.getElementById('musik-hex');
const communityHex = document.getElementById('community-hex');
const musikModal = document.getElementById('musik-modal');
const communityModal = document.getElementById('community-modal');
const musikModalClose = document.getElementById('musik-modal-close');
const communityModalClose = document.getElementById('community-modal-close');

// Mobile-Erkennung
function isMobileDevice() {
    return window.innerWidth <= 768;
}

// Hexagone für Mobile vorbereiten
function setupMobileHexagons() {
    if (isMobileDevice()) {
        // Hexagone zu Flip-Cards machen
        setupFlipCard(musikHex, 'MUSIK', 'Electronic meets Emotions: Das Musikalbum "EMBRACE" verbindet Sanftheit, Rhythmus und tiefe Texte.', 'https://open.spotify.com/intl-de/artist/3UtRmfHSN1nHEx3a0jWgwi', 'JETZT anhören');
        setupFlipCard(communityHex, 'COMMUNITY', 'Die Zukunft ins Jetzt holen: Die visionäre Community "DAS NEUE JETZT" verbindet die Pioniere und Empathen unserer Zeit.', 'https://www.dasneuejetzt.org', 'Jetzt ansehen');
    } else {
        // Flip-Card Struktur entfernen, falls vorhanden
        if (musikHex.classList.contains('flip-card')) {
            musikHex.classList.remove('flip-card');
            musikHex.innerHTML = `<div class="hex-content"><h2 class="hex-title">MUSIK</h2></div>`;
        }
        if (communityHex.classList.contains('flip-card')) {
            communityHex.classList.remove('flip-card');
            communityHex.innerHTML = `<div class="hex-content"><h2 class="hex-title">COMMUNITY</h2></div>`;
        }
    }
}

// Flip-Card erstellen
function setupFlipCard(element, title, text, linkUrl, linkText) {
    // Zuerst prüfen, ob das Element bereits eine Flip-Card ist
    if (!element.classList.contains('flip-card')) {
        element.classList.add('flip-card');
        
        // Ursprünglichen Inhalt speichern, um ihn bei Bedarf wiederherzustellen
        const originalContent = element.innerHTML;
        
        // Neue Flip-Card-Struktur erstellen
        element.innerHTML = `
            <div class="flip-card-inner">
                <div class="flip-card-front">
                    <h2 class="flip-card-title">${title}</h2>
                </div>
                <div class="flip-card-back">
                    <h2 class="flip-card-title">${title}</h2>
                    <p class="flip-card-text">${text}</p>
                    <a href="${linkUrl}" class="flip-card-button" target="_blank">${linkText}</a>
                </div>
            </div>
        `;
        
        // Click-Event hinzufügen
        element.addEventListener('click', function(e) {
            if (isMobileDevice()) {
                e.preventDefault();
                this.classList.toggle('flipped');
            }
        });
    }
}

// Lazy Loading für Bilder
document.addEventListener('DOMContentLoaded', function() {
    const lazyImages = document.querySelectorAll('img.lazy');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    image.classList.add('loaded');
                    observer.unobserve(image);
                }
            });
        });
        
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback für Browser ohne IntersectionObserver Support
        let lazyLoadThrottleTimeout;
        
        function lazyLoad() {
            if (lazyLoadThrottleTimeout) {
                clearTimeout(lazyLoadThrottleTimeout);
            }
            
            lazyLoadThrottleTimeout = setTimeout(function() {
                const scrollTop = window.pageYOffset;
                
                lazyImages.forEach(img => {
                    if (img.offsetTop < window.innerHeight + scrollTop) {
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                    }
                });
                
                if (lazyImages.length === 0) {
                    document.removeEventListener('scroll', lazyLoad);
                    window.removeEventListener('resize', lazyLoad);
                    window.removeEventListener('orientationChange', lazyLoad);
                }
            }, 20);
        }
        
        document.addEventListener('scroll', lazyLoad);
        window.addEventListener('resize', lazyLoad);
        window.addEventListener('orientationChange', lazyLoad);
        
        // Initial load
        lazyLoad();
    }
    
    // Neue Suchfunktionalität initialisieren
    initSearch();
    
    // Profilbild-Zoom-Funktionalität initialisieren
    initProfileImageZoom();
    
    // Mobile-Hexagone initialisieren
    setupMobileHexagons();
    
    // Event-Listener für Fenstergrößenänderungen
    window.addEventListener('resize', function() {
        setupMobileHexagons();
    });
});

// Profilbild-Zoom-Funktionalität
function initProfileImageZoom() {
    const profileImage = document.getElementById('profile-image');
    
    if (profileImage) {
        profileImage.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Verhindern, dass das Event zum Modal propagiert
            
            // Originalbild-Quelle holen
            const imgSrc = profileImage.getAttribute('src');
            if (imgSrc.startsWith('data:image')) {
                // Wenn das Bild noch nicht geladen ist, nehmen wir die richtige Quelle
                enlargedImage.src = profileImage.getAttribute('data-src');
            } else {
                enlargedImage.src = imgSrc;
            }
            
            // Modal anzeigen ohne andere zu schließen
            document.body.style.overflow = 'hidden';
            enlargedImageModal.classList.add('active');
            
            // Event-Stopgap: Verhindern, dass das Klick-Event geschlossen wird
            setTimeout(function() {
                const overlay = document.getElementById('enlarged-image-modal');
                if (overlay) {
                    const stopModalClose = function(event) {
                        event.stopPropagation();
                    };
                    
                    // Entferne alle existierenden Klick-Handler
                    overlay.removeEventListener('click', stopModalClose);
                    // Füge einen neuen hinzu
                    overlay.addEventListener('click', function(event) {
                        // Nur schließen, wenn direkt auf den Hintergrund geklickt wird
                        if (event.target === overlay) {
                            closeModal(enlargedImageModal);
                        }
                    });
                }
            }, 10);
        });
    }
}

// Modal-Funktionen
function openModal(modal) {
    // Sicherstellen, dass alle anderen Modals geschlossen sind
    closeAllModals();
    
    document.body.style.overflow = 'hidden';
    modal.classList.add('active');
    
    // Lade Bilder im Modal sofort, wenn es geöffnet wird
    const modalLazyImages = modal.querySelectorAll('img.lazy');
    modalLazyImages.forEach(img => {
        img.src = img.dataset.src;
        img.classList.add('loaded');
    });
}

function closeModal(modal) {
    document.body.style.overflow = '';
    modal.classList.remove('active');
    
    // Scroll-Position explizit zurücksetzen
    resetModalScroll(modal);
}

// Funktion zum Schließen aller Modals
function closeAllModals() {
    const allModals = document.querySelectorAll('.modal');
    allModals.forEach(modal => {
        modal.classList.remove('active');
        resetModalScroll(modal);
    });
    document.body.style.overflow = '';
}

// Funktion zum Zurücksetzen der Scroll-Position
function resetModalScroll(modal) {
    if (modal) {
        const scrollContent = modal.querySelector('.modal-scroll');
        if (scrollContent) {
            // Sicherstellen, dass der Scroll sofort zurückgesetzt wird
            setTimeout(() => {
                scrollContent.scrollTop = 0;
            }, 0);
        }
    }
}

// Event-Listener
headerTitle.addEventListener('click', (e) => {
    e.preventDefault();
    openModal(ueberMichModal);
});

// Hexagon Modal Funktionalität - nur für Desktop
if (musikHex) {
    musikHex.addEventListener('click', (e) => {
        // Nur auf Desktop öffnen wir das Modal
        if (!isMobileDevice()) {
            e.preventDefault();
            openModal(musikModal);
        }
    });
}

if (communityHex) {
    communityHex.addEventListener('click', (e) => {
        // Nur auf Desktop öffnen wir das Modal
        if (!isMobileDevice()) {
            e.preventDefault();
            openModal(communityModal);
        }
    });
}

if (musikModalClose) {
    musikModalClose.addEventListener('click', () => {
        closeModal(musikModal);
    });
}

if (communityModalClose) {
    communityModalClose.addEventListener('click', () => {
        closeModal(communityModal);
    });
}

// Über mich Modal öffnen
ueberMichLink.addEventListener('click', (e) => {
    e.preventDefault();
    openModal(ueberMichModal);
});

// Impressum Modal öffnen
impressumLink.addEventListener('click', (e) => {
    e.preventDefault();
    openModal(impressumModal);
});

// Modal schließen
ueberMichClose.addEventListener('click', () => {
    closeModal(ueberMichModal);
});

impressumClose.addEventListener('click', () => {
    closeModal(impressumModal);
});

if (searchResultsClose) {
    searchResultsClose.addEventListener('click', () => {
        closeModal(searchResultsModal);
    });
}

if (enlargedImageClose) {
    enlargedImageClose.addEventListener('click', () => {
        closeModal(enlargedImageModal);
    });
}

// Modals schließen bei Klick außerhalb
window.addEventListener('click', (e) => {
    if (e.target === ueberMichModal) {
        closeModal(ueberMichModal);
    }
    if (e.target === impressumModal) {
        closeModal(impressumModal);
    }
    if (e.target === searchResultsModal) {
        closeModal(searchResultsModal);
    }
    if (e.target === musikModal) {
        closeModal(musikModal);
    }
    if (e.target === communityModal) {
        closeModal(communityModal);
    }
    if (e.target === enlargedImageModal) {
        closeModal(enlargedImageModal);
    }
});

// Modals mit ESC-Taste schließen
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        if (ueberMichModal.classList.contains('active')) {
            closeModal(ueberMichModal);
        }
        if (impressumModal.classList.contains('active')) {
            closeModal(impressumModal);
        }
        if (searchResultsModal.classList.contains('active')) {
            closeModal(searchResultsModal);
        }
        if (musikModal.classList.contains('active')) {
            closeModal(musikModal);
        }
        if (communityModal.classList.contains('active')) {
            closeModal(communityModal);
        }
        if (enlargedImageModal.classList.contains('active')) {
            closeModal(enlargedImageModal);
        }
    }
});

// Levenshtein-Distanz-Funktion für fuzzy search
function levenshteinDistance(a, b) {
    if (a.length === 0) return b.length;
    if (b.length === 0) return a.length;

    const matrix = [];

    // Initialisiere Matrix
    for (let i = 0; i <= b.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
        matrix[0][j] = j;
    }

    // Fülle Matrix
    for (let i = 1; i <= b.length; i++) {
        for (let j = 1; j <= a.length; j++) {
            if (b.charAt(i - 1) === a.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1, // Substitution
                    matrix[i][j - 1] + 1,     // Einfügung
                    matrix[i - 1][j] + 1      // Löschung
                );
            }
        }
    }

    return matrix[b.length][a.length];
}

// Ähnlichkeit zwischen zwei Wörtern berechnen (0 bis 1, wobei 1 = identisch)
function similarity(a, b) {
    const distance = levenshteinDistance(a.toLowerCase(), b.toLowerCase());
    const maxLength = Math.max(a.length, b.length);
    return 1 - (distance / maxLength);
}

// Suchfunktionen
function initSearch() {
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    const clearButton = document.getElementById('clear-search');
    const searchInputWrapper = document.querySelector('.search-input-wrapper');
    
    // Inhalte für die Suche sammeln
    const searchableContent = collectSearchableContent();
    
    // Mobilansicht: Suchleiste ein-/ausklappen
    let isSearchExpanded = false;
    
    // Prüfen, ob mobile Ansicht vorliegt
    function isMobileView() {
        return window.innerWidth <= 768;
    }
    
    // Suchleiste ein-/ausklappen basierend auf Viewport
    function toggleSearchVisibility() {
        if (isMobileView()) {
            if (isSearchExpanded) {
                searchInputWrapper.classList.add('expanded');
            } else {
                searchInputWrapper.classList.remove('expanded');
            }
        } else {
            // Auf Desktop immer eingeblendet
            searchInputWrapper.classList.add('expanded');
        }
    }
    
    // Initialen Zustand setzen
    toggleSearchVisibility();
    
    // Event-Handler für Lupen-Icon zum Ein-/Ausklappen
    searchButton.addEventListener('click', function(e) {
        if (isMobileView()) {
            if (!isSearchExpanded) {
                isSearchExpanded = true;
                toggleSearchVisibility();
                searchInput.focus();
                e.stopPropagation();
                return;
            }
        }
        // Falls die Suche bereits erweitert ist oder Desktop-Ansicht vorliegt, normal suchen
        executeSearch();
    });
    
    // Klick außerhalb der Suche in mobilem Modus schließt Suchleiste
    document.addEventListener('click', function(e) {
        if (isMobileView() && isSearchExpanded) {
            if (!document.querySelector('.search-container').contains(e.target) && !searchResultsModal.contains(e.target)) {
                isSearchExpanded = false;
                toggleSearchVisibility();
            }
        }
    });
    
    // Bei Größenänderung des Fensters Suchleisten-Sichtbarkeit anpassen
    window.addEventListener('resize', function() {
        toggleSearchVisibility();
    });
    
    // Event-Handler-Funktionen
    const updateClearButton = () => {
        clearButton.style.display = searchInput.value.trim() ? 'block' : 'none';
    };
    
    const executeSearch = () => {
        const query = searchInput.value.trim();
        if (query && query.length >= 2) {
            const results = performSearch(query, searchableContent);
            displaySearchResults(results, query);
        } else {
            searchResultsModal.classList.remove('active');
        }
    };
    
    // Event-Listener
    searchInput.addEventListener('input', (e) => {
        updateClearButton();
        executeSearch();
        e.stopPropagation();
    });
    
    clearButton.addEventListener('click', (e) => {
        searchInput.value = '';
        updateClearButton();
        searchInput.focus();
        searchResultsModal.classList.remove('active');
        e.stopPropagation();
    });
    
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            executeSearch();
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Event-Listener für Ergebnisauswahl
    document.addEventListener('click', event => {
        const resultItem = event.target.closest('.search-result-item');
        if (resultItem) {
            handleResultSelection(event);
        }
    });
}

// Sammel alle durchsuchbaren Inhalte
function collectSearchableContent() {
    const searchableContent = [];
    
    // Über Mich Modal Inhalt
    const ueberMichModal = document.getElementById('ueber-mich-modal');
    if (ueberMichModal) {
        searchableContent.push({
            id: 'ueber-mich-modal',
            title: 'Jana Sophie Breitmar',
            content: ueberMichModal.textContent,
            type: 'page',
            snippet: 'Persönliche Informationen über Jana Sophie Breitmar'
        });
    }
    
    // Impressum & Datenschutz Modal Inhalt
    const impressumModal = document.getElementById('impressum-modal');
    if (impressumModal) {
        searchableContent.push({
            id: 'impressum-modal',
            title: 'Impressum & Datenschutz',
            content: impressumModal.textContent,
            type: 'page',
            snippet: 'Rechtliche Informationen und Datenschutzerklärung'
        });
    }
    
    // EMBRACE Album Inhalt
    searchableContent.push({
        id: 'album-hex',
        title: 'EMBRACE',
        content: 'Musikalbum, Spotify, Neues Album, Musik',
        type: 'project',
        snippet: 'Das neue Musikalbum von Jana Sophie Breitmar'
    });
    
    // DAS NEUE JETZT Projekt Inhalt
    searchableContent.push({
        id: 'project-hex',
        title: 'DAS NEUE JETZT',
        content: 'Zukunftsprojekt, Zukunft ins Jetzt holen, Vision',
        type: 'project',
        snippet: 'Jana Sophie Breitmars Zukunftsprojekt'
    });
    
    return searchableContent;
}

// Suche durchführen
function performSearch(query, searchableContent) {
    if (!query || query.trim() === '') return [];
    
    const results = [];
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
    const SIMILARITY_THRESHOLD = 0.6;
    
    searchableContent.forEach(item => {
        let score = 0;
        const content = (item.title + ' ' + item.content).toLowerCase();
        const contentWords = content.split(/\s+|\.|,|:|;|-|\(|\)/).filter(word => word.length > 1);
        
        queryTerms.forEach(term => {
            // Exakte Übereinstimmung
            if (content.includes(term)) {
                score += 2;
            }
            
            // Ähnliche Wörter finden
            contentWords.forEach(word => {
                const wordSimilarity = similarity(term, word);
                if (wordSimilarity >= SIMILARITY_THRESHOLD) {
                    score += wordSimilarity;
                }
            });
        });
        
        if (score > 0) {
            results.push({
                ...item,
                score: score
            });
        }
    });
    
    // Sortiere Ergebnisse nach Score (absteigend)
    return results.sort((a, b) => b.score - a.score);
}

// Highlight-Funktion für Suchergebnisse
function highlightSearchTerms(text, query) {
    if (!query || query.trim() === '') return text;
    
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
    let highlightedText = text;
    
    queryTerms.forEach(term => {
        const regex = new RegExp('(' + term + ')', 'gi');
        highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });
    
    return highlightedText;
}

// Suchergebnisse anzeigen
function displaySearchResults(results, query) {
    const searchResultsContent = document.getElementById('search-results-content');
    
    if (!searchResultsModal || !searchResultsContent) return;
    
    // Leere bisherige Ergebnisse
    searchResultsContent.innerHTML = '';
    
    if (results.length === 0) {
        searchResultsContent.innerHTML = `
            <div class="no-results">
                <p>Keine Ergebnisse für "<strong>${query}</strong>" gefunden.</p>
                <p>Versuche es mit anderen Suchbegriffen oder weniger spezifischen Begriffen.</p>
            </div>
        `;
    } else {
        // Ergebnistitel
        const resultsTitle = document.createElement('h2');
        resultsTitle.textContent = `${results.length} Ergebnis${results.length !== 1 ? 'se' : ''} für "${query}"`;
        searchResultsContent.appendChild(resultsTitle);
        
        // Ergebnisliste
        const resultsList = document.createElement('div');
        resultsList.className = 'search-results-list';
        
        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';
            resultItem.setAttribute('data-id', result.id);
            resultItem.setAttribute('data-type', result.type);
            
            // Icon je nach Ergebnistyp
            let iconClass = '';
            switch(result.type) {
                case 'project': iconClass = 'project-icon'; break;
                case 'page': iconClass = 'page-icon'; break;
            }
            
            resultItem.innerHTML = `
                <div class="result-header">
                    <span class="result-icon ${iconClass}"></span>
                    <h3 class="result-title">${highlightSearchTerms(result.title, query)}</h3>
                </div>
                <p class="result-snippet">${highlightSearchTerms(result.snippet, query)}</p>
            `;
            
            resultsList.appendChild(resultItem);
        });
        
        searchResultsContent.appendChild(resultsList);
    }
    
    // Modal öffnen
    openModal(searchResultsModal);
}

// Suchergebnisauswahl verarbeiten
function handleResultSelection(event) {
    const resultItem = event.target.closest('.search-result-item');
    if (!resultItem) return;
    
    const itemId = resultItem.getAttribute('data-id');
    const itemType = resultItem.getAttribute('data-type');
    
    // Suchergebnis-Modal schließen
    closeModal(searchResultsModal);
    
    // Je nach Typ des Ergebnisses eine Aktion ausführen
    switch(itemType) {
        case 'page':
            // Modal öffnen
            const modal = document.getElementById(itemId);
            if (modal) {
                openModal(modal);
            }
            break;
        case 'project':
            // Zu Projekt scrollen und hervorheben
            const projectElement = document.querySelector(`.${itemId}`);
            if (projectElement) {
                projectElement.scrollIntoView({ behavior: 'smooth' });
                setTimeout(() => {
                    projectElement.classList.add('highlight');
                    setTimeout(() => {
                        projectElement.classList.remove('highlight');
                    }, 1500);
                }, 500);
            }
            break;
    }
}