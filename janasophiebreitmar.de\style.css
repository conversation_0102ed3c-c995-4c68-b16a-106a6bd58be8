/* Hexagon-Inhalte im Modal: Text über dem Bild */
.hex-modal-title, .hex-modal-text, .hex-modal-button {
    position: relative;
    z-index: 5; /* Über dem Bild und der Overlay-Schicht */
}/* Einbinden der Schriftarten */
@font-face {
    font-display: swap;
    font-family: 'Caveat';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Caveat';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 300;
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 300;
    src: url('assets/fonts/open-sans-v40-latin-300.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/open-sans-v40-latin-regular.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    src: url('assets/fonts/open-sans-v40-latin-600.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/open-sans-v40-latin-700.woff2') format('woff2');
}

/* CSS Reset und Basis-Styling */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --dark-gray: #222222;
    --mustard-yellow: #e9d16f;
    --light-mustard: #f2dc85;
    --dark-mustard: #c9b45f;
    --off-white: #f9f9f9;
    --text-color: #444;
    --header-height: 40px;
    --footer-height: 40px;
    --hex-size: 80px;
    --hex-size-large: 240px;
    --hex-border-width: 2px;
}

html, body {
    height: 100%;
    font-family: 'Open Sans', Arial, sans-serif;
    color: var(--text-color);
    scroll-behavior: smooth;
    overflow-y: hidden; /* Verhindert vertikales Scrollen */
}

body {
    overflow-x: hidden;
    background-color: var(--off-white);
    display: flex;
    flex-direction: column;
}

/* Header Styling - kleiner wie dasneuejetzt.org */
header {
    background-color: var(--dark-gray);
    height: var(--header-height);
    display: flex;
    justify-content: center;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0 15px;
}

.header-title {
    font-family: 'Montserrat', sans-serif;
    color: var(--mustard-yellow);
    font-size: 0.9rem; /* Kleinere Schrift für filigranen Look */
    font-weight: 500; /* Weniger fett */
    letter-spacing: 2px;
    text-decoration: none;
    transition: color 0.3s ease;
    text-transform: uppercase; /* Großbuchstaben */
}

.header-title:hover {
    color: var(--light-mustard);
}

/* Main Content Styling */
main {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Hero Section */
.hero-section {
    width: 100%;
    height: calc(100vh - var(--header-height) - var(--footer-height));
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Hintergrundbild */
.background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

/* MEIN PORTFOLIO Überschrift im Stil von "NEUE ZWISCHENMENSCHLICHKEIT" */
.portfolio-title {
    position: relative;
    margin-top: 40px;
    font-size: 36px;
    font-weight: bold;
    color: #222222;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2), 0 0 20px rgba(255, 255, 255, 0.1);
    -webkit-text-stroke: 1px rgba(255, 255, 255, 0.5);
    background-color: transparent;
    padding: 10px 20px;
    z-index: 5;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 2px;
    line-height: 1.5;
    text-transform: uppercase;
}

/* Lazy Loading für Bilder */
.lazy {
    opacity: 0;
    transition: opacity 0.5s ease-in-out, filter 0.5s ease-in-out;
    filter: blur(15px);
    transform: scale(1.02);
}

.lazy.loaded {
    opacity: 1;
    filter: blur(0);
    transform: scale(1);
}

/* Feature Hexagons Container */
.feature-hexagons {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 15%;
    z-index: 10;
    margin: auto 0 50px 0;
}

/* Basis-Styling für Feature Hexagons */
.feature-hex {
    width: 180px; /* Kleinere Hexagons */
    height: 156px; /* Höhe = Breite * sin(60°) */
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    background-color: rgba(0, 0, 0, 0.7);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    animation: pulse 3s infinite; /* Pulsationsanimation */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Flip-Karten Styling für mobile Geräte */
.feature-hex.flip-card {
    perspective: 1000px;
    background-color: transparent;
    animation: none;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.feature-hex.flip-card.flipped .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.7);
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}

.flip-card-front {
    color: var(--mustard-yellow);
}

.flip-card-back {
    transform: rotateY(180deg);
    color: var(--mustard-yellow);
}

.flip-card-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--mustard-yellow);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
    text-align: center;
}

.flip-card-text {
    font-size: 0.9rem;
    color: var(--mustard-yellow);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.4;
    text-align: center;
    margin-bottom: 15px;
}

.flip-card-button {
    background-color: var(--mustard-yellow);
    color: var(--dark-gray);
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 25px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: inline-block;
}

.flip-card-button:hover {
    background-color: var(--dark-mustard);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

/* Hover-Effekt: Gold-Glitzer und Größenänderung */
.feature-hex:hover {
    transform: scale(1.05);
    animation: hex-glitter 1.5s infinite;
    background-color: rgba(0, 0, 0, 0.6);
}

.feature-hex.flip-card:hover {
    transform: none;
    animation: none;
    background-color: transparent;
}

/* Glitzer-Animation für Hover */
@keyframes hex-glitter {
    0% {
        box-shadow: 0 0 5px rgba(233, 209, 111, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(233, 209, 111, 0.7), 0 0 30px rgba(233, 209, 111, 0.4);
    }
    100% {
        box-shadow: 0 0 5px rgba(233, 209, 111, 0.3);
    }
}

/* Pulse-Animation für die Sechsecke */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
}

/* Hintergrundbilder für Hexagons (versteckt im normalen Zustand) */
.album-hex::before, .project-hex::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0; /* Komplett versteckt im normalen Zustand */
    z-index: -1;
}

/* Hintergrundbilder nur für die Rückseite der Flip-Cards */
.album-hex .flip-card-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('assets/bilder/embrace.png');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: -1;
}

.project-hex .flip-card-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('assets/bilder/dasneuejetzt.png');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: -1;
}

/* Hintergrund für die Vorderseite */
.flip-card-front {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Hexagon-Inhalte */
.hex-content {
    z-index: 2;
    text-align: center;
    width: 100%;
}

.hex-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--mustard-yellow);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
    padding: 0 10px;
}

.hex-text {
    font-size: 0.9rem;
    color: var(--mustard-yellow);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.4;
    padding: 0 20px;
    display: none; /* Ausgeblendet im normalen Modus */
    margin-top: 15px;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* Hexagon Modal Styling */
.hex-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    justify-content: center;
    align-items: center;
    background-color: hsl(from var(--mustard-yellow) h s l / 19%);
}

.hex-modal.active {
    display: flex;
}

.hex-modal-content {
    width: 350px;
    height: 304px; /* Höhe = Breite * sin(60°) */
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
    position: relative;
    overflow: hidden;
    transform: scale(0.5);
    opacity: 0;
    transition: transform 0.4s ease, opacity 0.4s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.hex-modal.active .hex-modal-content {
    transform: scale(1);
    opacity: 1;
}

.hex-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: -2; /* Hinter der Overlay-Schicht */
}

/* Overlay-Schicht über dem Bild */
.hex-modal-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7); /* Dunkel-transparente Schicht */
    z-index: -1; /* Über dem Bild, unter dem Text */
}

.album-modal .hex-modal-content::before {
    background-image: url('assets/bilder/embrace.png');
}

.project-modal .hex-modal-content::before {
    background-image: url('assets/bilder/dasneuejetzt.png');
}

.hex-modal-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    color: var(--mustard-yellow);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 20px;
    text-align: center;
}

.hex-modal-text {
    font-size: 1rem;
    color: var(--mustard-yellow);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.5;
    text-align: center;
    margin-bottom: 25px;
}

.hex-modal-button {
    background-color: var(--mustard-yellow);
    color: var(--dark-gray);
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
    padding: 12px 25px;
    border-radius: 25px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    display: inline-block;
    margin-top: 10px;
}

.hex-modal-button:hover {
    background-color: var(--dark-mustard);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.hex-modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Moderne Buttons in Hexagonen */
.hex-button {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px; /* Höhe des Button-Bereichs */
    background-color: var(--mustard-yellow);
    color: var(--dark-gray);
    text-decoration: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 5;
    border-radius: 0 0 25px 25px; /* Abgerundete Ecken am unteren Rand */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Subtiler Schatten */
    overflow: hidden; /* Für den Hover-Effekt */
    cursor: pointer;
}

.hex-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    z-index: -1;
}

.hex-button:hover {
    background-color: var(--dark-mustard);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.hex-button:hover:before {
    left: 100%;
}

.button-text {
    position: relative;
    z-index: 2;
}

/* Footer Styling - kleiner */
footer {
    background-color: var(--dark-gray);
    height: var(--footer-height);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--mustard-yellow);
    font-size: 0.8rem;
    z-index: 100;
}

.footer-links {
    display: flex;
    gap: 10px;
    align-items: center;
}

.footer-link {
    color: var(--mustard-yellow);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover, .footer-link:focus {
    color: var(--light-mustard);
    text-decoration: underline;
}

.footer-separator {
    color: var(--mustard-yellow);
    margin: 0 5px;
}

.copyright {
    color: var(--mustard-yellow);
    margin-right: 5px;
}

/* Modal Styling */
.modal {
    position: absolute;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100% - var(--header-height) - var(--footer-height));
    background-color: hsl(from var(--mustard-yellow) h s l / 19%);
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1100;
    overflow-y: auto;
}

.modal.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    background-color: white;
    max-width: 800px;
    width: 90%;
    margin: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: scale(1);
}

/* Spezielle Stile für Impressum-Modal und Über mich-Modal */
.impressum-modal-content,
.ueber-mich-modal-content {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    margin: auto;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    background-color: rgba(34, 34, 34, 0.95); /* Dunkler Hintergrund mit leichter Transparenz */
    border-radius: 10px;
    color: var(--mustard-yellow); /* Text in Gelb */
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    color: var(--mustard-yellow); /* Schließen-Button in Gelb */
    background: none;
    border: none;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover, .modal-close:focus {
    background-color: rgba(233, 209, 111, 0.2); /* Leicht gelber Hintergrund beim Hover */
    outline: none;
}

.content-scroll,
.modal-scroll {
    padding: 40px;
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: auto;
}

/* Über mich Modal Styling */
.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    text-align: center;
}

.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 3px solid var(--mustard-yellow);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.profile-name {
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: var(--mustard-yellow);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.profile-title {
    font-family: 'Caveat', cursive;
    font-size: 1.7rem;
    color: var(--mustard-yellow);
    margin-bottom: 15px;
    font-weight: 600;
}

.section {
    margin-bottom: 40px;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 1.2rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(233, 209, 111, 0.3);
    color: var(--mustard-yellow);
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* Impressum und Datenschutz-Formatierungen */
#impressum-modal .section p,
#impressum-modal .section li,
#ueber-mich-modal .section p,
#ueber-mich-modal .section li {
    margin-bottom: 15px;
    line-height: 1.6;
    font-family: 'Open Sans', Arial, sans-serif;
    color: var(--light-mustard); /* Textfarbe etwas heller als Überschriften */
}

#impressum-modal .section strong,
#ueber-mich-modal .section strong {
    font-weight: 600;
    color: var(--mustard-yellow); /* Hervorgehobener Text in kräftigem Gelb */
}

.quote {
    font-style: italic;
    color: var(--dark-gray);
    padding: 15px;
    border-left: 3px solid var(--dark-gray);
    margin: 20px 0;
    background-color: var(--mustard-yellow);
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.skill-label {
    display: inline-block;
    background-color: rgba(34, 34, 34, 0.95);
    color: var(--mustard-yellow);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    border: 1px solid var(--mustard-yellow);
}

.skill-label:hover {
    background-color: rgba(34, 34, 34, 0.85);
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(233, 209, 111, 0.3);
}

.social-links {
    display: flex;
    justify-content: flex-start;
    gap: 25px;
    margin-top: 10px;
}

.social-link {
    color: var(--mustard-yellow);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s;
    padding: 8px 15px;
    border-radius: 5px;
}

.social-link:hover, .social-link:focus {
    color: var(--light-mustard);
    text-decoration: underline;
    outline: none;
    background-color: rgba(233, 209, 111, 0.1);
}

/* Animation für die Hexagone */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animated {
    animation: fadeIn 0.8s ease forwards;
}

/* Smooth scrollbar für webkit browser */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: rgba(34, 34, 34, 0.8); /* Dunklere Scrollbar-Spur */
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(233, 209, 111, 0.5); /* Gelbe Scrollbar */
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(233, 209, 111, 0.7); /* Hellere gelbe Scrollbar beim Hover */
}

/* Hervorhebungseffekt für Suchergebnisse */
.highlight {
    animation: highlight-pulse 1.5s ease-in-out;
}

@keyframes highlight-pulse {
    0% { box-shadow: 0 0 0 0 rgba(233, 209, 111, 0.7); }
    70% { box-shadow: 0 0 0 20px rgba(233, 209, 111, 0); }
    100% { box-shadow: 0 0 0 0 rgba(233, 209, 111, 0); }
}

/* Suchcontainer Styling */
.search-container {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    height: 28px;
    max-width: 200px;
    border-radius: 14px;
    background-color: rgba(255, 255, 255, 0.15);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-container:focus-within {
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 5px rgba(233, 209, 111, 0.5);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    transition: all 0.3s ease;
}

.search-input {
    width: 160px;
    height: 100%;
    border: none;
    padding: 0 30px 0 10px; /* Abstand rechts für X-Button */
    font-size: 13px;
    color: var(--mustard-yellow);
    background: transparent;
    outline: none;
    transition: width 0.3s ease, opacity 0.3s ease;
    font-family: 'Open Sans', Arial, sans-serif;
}

.search-input::placeholder {
    color: rgba(233, 209, 111, 0.7);
    font-family: 'Open Sans', Arial, sans-serif;
}

.clear-search {
    position: absolute;
    right: 24px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: rgba(233, 209, 111, 0.5);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    width: 14px;
    height: 14px;
    z-index: 2;
}

.clear-search:hover {
    color: rgba(233, 209, 111, 0.9);
}

.search-button {
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(233, 209, 111, 0.7);
    transition: color 0.3s ease;
    z-index: 2;
}

.search-button:hover {
    color: var(--mustard-yellow);
}

/* Suchergebnisse Modal Styling */
#search-results-modal {
    position: absolute;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100% - var(--header-height) - var(--footer-height));
    background-color: hsl(from var(--mustard-yellow) h s l / 19%);
    display: none;
    z-index: 1050;
    pointer-events: none;
}

#search-results-modal.active {
    display: block;
    pointer-events: auto;
}

.search-results-modal-content {
    position: absolute;
    top: 40px; /* Direkt unter der Suchleiste */
    left: 15px; /* Gleiche Position wie die Suchleiste */
    transform: none;
    max-width: 550px;
    width: 90%;
    max-height: 80vh;
    background-color: rgba(34, 34, 34, 0.95); /* Dunkler Hintergrund mit leichter Transparenz */
    padding: 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    color: var(--mustard-yellow); /* Textfarbe in Gelb */
}

#search-results-modal.active .search-results-modal-content {
    opacity: 1;
    transform: translateY(0);
}

#search-results-content {
    padding: 18px;
    overflow-y: auto;
    max-height: calc(80vh - 20px);
    scrollbar-width: thin;
    scrollbar-color: rgba(233, 209, 111, 0.4) rgba(34, 34, 34, 0.8);
    padding-right: 10px;
}

#search-results-content::-webkit-scrollbar {
    width: 5px;
    position: absolute;
    right: 0;
}

#search-results-content::-webkit-scrollbar-track {
    background: rgba(34, 34, 34, 0.8);
    border-radius: 3px;
    margin-top: 5px;
    margin-bottom: 5px;
}

#search-results-content::-webkit-scrollbar-thumb {
    background-color: rgba(233, 209, 111, 0.4);
    border-radius: 3px;
}

.search-results-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
    width: 100%;
}

.search-result-item {
    padding: 12px;
    border-radius: 6px;
    background-color: rgba(233, 209, 111, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 2px solid transparent;
    width: 100%;
}

.search-result-item:hover {
    background-color: rgba(233, 209, 111, 0.1);
    border-left-color: rgba(233, 209, 111, 0.5);
    transform: translateX(2px);
}

.result-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.result-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.7;
}

.project-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23e9d16f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>');
}

.page-icon {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23e9d16f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
}

.result-title {
    font-size: 15px;
    font-weight: 500;
    color: var(--mustard-yellow); /* Titel in Gelb */
    margin: 0;
    font-family: 'Montserrat', sans-serif;
}

.result-snippet {
    font-size: 13px;
    color: var(--light-mustard); /* Snippet leicht heller */
    margin: 0;
    line-height: 1.4;
    font-family: 'Open Sans', Arial, sans-serif;
}

.no-results {
    text-align: center;
    padding: 18px 0;
    color: var(--light-mustard);
    font-family: 'Open Sans', Arial, sans-serif;
    font-size: 14px;
}

mark {
    background-color: rgba(34, 34, 34, 0.95);
    color: var(--mustard-yellow);
    padding: 0 3px;
    border-radius: 2px;
    border: 1px solid var(--mustard-yellow);
}

/* Anpassung für den Schließen-Button im Suchergebnisse-Modal */
#search-results-modal .modal-close {
    position: absolute; 
    top: 8px;
    right: 8px;
    z-index: 1060;
}

#search-results-modal h2 {
    font-size: 16px;
    color: var(--mustard-yellow);
    font-weight: 500;
    margin-bottom: 10px;
}

/* Vergrößertes Bild Modal Styling */
.enlarged-image-modal-content {
    background-color: transparent;
    box-shadow: none;
    max-width: 90%;
    width: auto;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    margin: auto;
}

.enlarged-image {
    max-width: 80vw;
    max-height: 80vh;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--mustard-yellow);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    width: 300px;
    height: 300px;
}

#enlarged-image-modal .modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.8rem;
    z-index: 1200;
}

#enlarged-image-modal .modal-close:hover {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* Musik-Streaming-Overlay */
.music-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: rgba(0, 0, 0, 0.9);
    transition: height 0.3s ease;
    z-index: 10;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.music-overlay.active {
    height: 100%;
}

.music-overlay-title {
    color: var(--mustard-yellow);
    font-family: 'Montserrat', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.music-platforms {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 80%;
}

.music-platform-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.music-platform-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.platform-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.platform-name {
    font-family: 'Montserrat', sans-serif;
    font-size: 0.9rem;
}

.close-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 11;
}

.close-overlay:hover {
    color: var(--mustard-yellow);
}

/* Responsive Anpassungen */
@media (max-width: 1200px) {
    :root {
        --hex-size: 70px;
        --hex-size-large: 200px;
        --hex-border-width: 1.5px;
    }
    
    .large .hex-title {
        font-size: 1.6rem;
    }
}

@media (max-width: 992px) {
    :root {
        --hex-border-width: 1.5px;
    }
    
    .header-title {
        font-size: 0.85rem;
    }
    
    .feature-hex {
        width: 160px;
        height: 139px;
    }
    
    .hex-title {
        font-size: 1.3rem;
    }
    
    .hex-modal-content {
        width: 300px;
        height: 260px;
    }
    
    .hex-modal-title {
        font-size: 1.7rem;
    }
    
    .hex-modal-text {
        font-size: 0.9rem;
    }

    .enlarged-image {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 768px) {
    :root {
        --hex-border-width: 1.2px;
        --header-height: 60px; /* Größerer Header für Mobile */
    }
    
    .header-title {
        font-size: 0.8rem;
    }
    
    .hero-section {
        height: calc(100vh - var(--header-height) - var(--footer-height));
        min-height: unset; /* Entfernt min-height, damit die Sektion genau in den verfügbaren Platz passt */
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    .feature-hexagons {
        position: static;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        margin: 0;
        margin-bottom: 50px;
        transform: none;
        justify-content: flex-end;
    }
    
    .feature-hex {
        width: 220px; /* Kleinere Hexagons für Mobile */
        height: 191px;
        animation: none;
    }
    
    .hex-title {
        font-size: 1.4rem;
    }
    
    /* Anzeigen der Flip-Card nur auf mobilen Geräten */
    .feature-hex .hex-content {
        display: none;
    }
    
    .feature-hex.flip-card .flip-card-inner {
        display: block;
    }
    
    .modal-scroll, .content-scroll {
        padding: 30px;
    }
    
    .profile-image {
        width: 120px;
        height: 120px;
    }
    
    .portfolio-title {
        font-size: 22px;
        position: static;
        margin-bottom: 30px;
        transform: none;
        letter-spacing: 1px;
    }

    .enlarged-image {
        width: 200px;
        height: 200px;
    }
    
    /* Mobile Suchleisten-Styling */
    .search-input-wrapper {
        width: 28px;
        overflow: hidden;
    }
    
    .search-input-wrapper.expanded {
        width: 100%;
    }
    
    .search-input {
        opacity: 0;
        position: absolute;
        left: 0;
        width: calc(100% - 28px);
    }
    
    .search-input-wrapper.expanded .search-input {
        opacity: 1;
        position: relative;
    }
    
    .clear-search {
        opacity: 0;
        pointer-events: none;
    }
    
    .search-input-wrapper.expanded .clear-search {
        opacity: 1;
        pointer-events: auto;
    }
    
    .search-container {
        max-width: 150px;
    }
    
    .search-input {
        width: 110px;
        font-size: 13px;
    }
    
    .search-results-modal-content {
        width: calc(100% - 30px);
        max-width: none;
    }
}

@media (max-width: 576px) {
    :root {
        --hex-border-width: 1px;
        --header-height: 60px;
    }
    
    .header-title {
        font-size: 0.75rem;
    }
    
    .feature-hex {
        width: 200px; /* Noch kleinere Hexagons für sehr kleine Geräte */
        height: 173px;
    }
    
    .flip-card-title {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .flip-card-text {
        font-size: 0.75rem;
        line-height: 1.2;
        margin-bottom: 8px;
    }
    
    .flip-card-button {
        font-size: 0.7rem;
        padding: 5px 10px;
    }
}
    
    .hex-title {
        font-size: 1.3rem;
    }
    
    .hex-modal-content {
        width: 240px;
        height: 208px;
    }
    
    .hex-modal-title {
        font-size: 1.3rem;
        margin-bottom: 10px;
    }
    
    .hex-modal-text {
        font-size: 0.8rem;
        margin-bottom: 15px;
    }
    
    .hex-modal-button {
        font-size: 0.75rem;
        padding: 8px 18px;
    }
    
    .footer-links {
        gap: 10px;
        padding: 5px 0;
        font-size: 0.7rem;
    }
    
    .modal-scroll, .content-scroll {
        padding: 20px;
    }
    
    .profile-image {
        width: 100px;
        height: 100px;
    }

    .enlarged-image {
        width: 180px;
        height: 180px;
    }
