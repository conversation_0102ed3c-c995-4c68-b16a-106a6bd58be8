<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Neue Harmonie - Kompatibilitäts-Kartenset</title>
    
    <!-- Meta Description -->
    <meta name="description" content="Erkunde tiefere Ebenen von Kompatibilität mit dem Kartenset 'Neue Harmonie'. Entdecke Gemeinsamkeiten und Unterschiede in Werten, Visionen und Lebensgestaltung.">

    <!-- Fonts - Mit zusätzlicher Serif-Schrift -->
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;600;700&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Open Sans', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f9f9f9;
            color: #333;
            overflow-x: hidden;
        }
        
        /* Header und Footer */
        .header, .footer {
            background-color: #222222;
            color: #7c6cd8;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            width: 100%;
        }
        
        .header {
            height: 40px;
            font-size: 18px;
            font-family: 'Caveat', 'Segoe Script', cursive;
            position: sticky;
            top: 0;
            z-index: 40;
        }
        
        .header-link {
            color: #7c6cd8;
            text-decoration: none;
            font-family: 'Caveat', 'Segoe Script', cursive;
            font-size: 20px;
        }
        
        .footer {
            height: 40px;
            font-size: 14px;
            padding: 0 15px;
            text-align: center;
            flex-wrap: wrap;
            margin-top: auto;
        }
        
        .footer a {
            color: #7c6cd8;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        /* Hauptinhalt */
        .container {
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 20px 15px 40px;
            flex-grow: 1;
            position: relative;
            z-index: 1;
        }
        
        .intro-section {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 40px;
            padding: 30px;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
        }
        
        .main-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 32px;
            color: #7c6cd8;
            margin-bottom: 24px;
            position: relative;
            display: inline-block;
            line-height: 1.3;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -10px;
            width: 60px;
            height: 3px;
            background-color: #7c6cd8;
            transform: translateX(-50%);
            border-radius: 3px;
        }
        
        .intro-text {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 0;
            color: #444;
            padding: 0 10px;
        }
        
        /* Kartenstapel-Bereich */
        .card-decks {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 35px;
            margin-bottom: 40px;
        }
        
        .deck {
            position: relative;
            width: 200px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            perspective: 1000px;
        }
        
        .deck:hover {
            transform: translateY(-8px);
        }
        
        .deck-instruction {
            position: absolute;
            top: -25px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 13px;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.4px;
            text-shadow: 0 0 5px white;
        }
        
        /* Kategoriespezifische Farben für Deck-Anweisungen */
        .deck-all .deck-instruction { color: #7c6cd8; }
        .deck-grundlegendes .deck-instruction { color: #ff7e6b; }
        .deck-freundschaft .deck-instruction { color: #9c6bff; }
        .deck-partnerschaft .deck-instruction { color: #64ddff; }
        .deck-sexualitaet .deck-instruction { color: #ffc764; }
        
        .deck:hover .deck-instruction {
            opacity: 1;
        }
        
        /* Modernere Titel mit Kategorie-Farben */
        .deck-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 17px;
            text-align: center;
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
            letter-spacing: 0.3px;
        }
        
        /* Kategoriespezifische Farben für Titel */
        .deck-all .deck-title { 
            background: linear-gradient(135deg, #7c6cd8, #9384e8); 
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .deck-grundlegendes .deck-title { 
            background: linear-gradient(135deg, #ff7e6b, #ff9d8e); 
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .deck-freundschaft .deck-title { 
            background: linear-gradient(135deg, #9c6bff, #b18fff); 
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .deck-partnerschaft .deck-title { 
            background: linear-gradient(135deg, #64ddff, #8de6ff); 
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .deck-sexualitaet .deck-title { 
            background: linear-gradient(135deg, #ffc764, #ffd68c); 
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .deck-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            border-radius: 2px;
        }
        
        /* Kategoriespezifische Farben für Titel-Unterstriche */
        .deck-all .deck-title::after { background-color: #7c6cd8; }
        .deck-grundlegendes .deck-title::after { background-color: #ff7e6b; }
        .deck-freundschaft .deck-title::after { background-color: #9c6bff; }
        .deck-partnerschaft .deck-title::after { background-color: #64ddff; }
        .deck-sexualitaet .deck-title::after { background-color: #ffc764; }
        
        .cards-container {
            position: relative;
            height: 280px;
            perspective: 1200px;
        }
        
        .card {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 16px;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            transition: transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s ease;
            transform-style: preserve-3d;
            background-color: white;
            overflow: hidden;
        }
        
        .card.stacked {
            top: 0;
            left: 0;
        }
        
        /* Anpassung für besseren Stapel-Effekt */
        .card.stacked:nth-child(1) { transform: translateZ(0) translateX(0) translateY(0); }
        .card.stacked:nth-child(2) { transform: translateZ(-1px) translateX(2px) translateY(2px); }
        .card.stacked:nth-child(3) { transform: translateZ(-2px) translateX(4px) translateY(4px); }
        .card.stacked:nth-child(4) { transform: translateZ(-3px) translateX(6px) translateY(6px); opacity: 0.9; }
        .card.stacked:nth-child(5) { transform: translateZ(-4px) translateX(8px) translateY(8px); opacity: 0.8; }
        
        /* Deck-Hover-Effekt mit verbesserter Animation */
        .deck:hover .card.stacked:nth-child(1) { 
            transform: translateZ(5px) translateX(0) translateY(-5px); 
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12); 
        }
        
        /* VERBESSERTE KARTENCOVER: Mit fixem Seitenverhältnis */
        .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 16px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
        }
        
        .card-image-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        
        .card-cover-img {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        /* Eleganteres Deck-Label */
        .deck-label {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 15px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 1.5px;
            z-index: 2;
        }
        
        /* Moderneres Karten-Modal mit Wrapper */
        .card-modal-wrapper {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        .card-modal-wrapper.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* Sanftere, modernere Hintergrundfarben */
        .card-modal-wrapper.all-category {
            background-color: rgba(124, 108, 216, 0.75);
        }
        
        .card-modal-wrapper.grundlegendes-category {
            background-color: rgba(255, 126, 107, 0.75);
        }
        
        .card-modal-wrapper.freundschaft-category {
            background-color: rgba(156, 107, 255, 0.75);
        }
        
        .card-modal-wrapper.partnerschaft-category {
            background-color: rgba(100, 221, 255, 0.75);
        }
        
        .card-modal-wrapper.sexualitaet-category {
            background-color: rgba(255, 199, 100, 0.75);
        }
        
        /* UI-Elemente für die Kategorie ÜBER der Karte */
        .card-category-label {
            display: inline-block;
            font-size: 12px;
            font-weight: 600;
            padding: 6px 14px;
            border-radius: 30px;
            margin-bottom: 18px;
            font-family: 'Montserrat', sans-serif;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
        }
        
        /* NEUES DESIGN: Modal-Container für die Karte selbst */
        .modal-card {
            width: 320px;
            background-color: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            transform: scale(0.9) translateY(40px);
            opacity: 0;
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            margin-bottom: 24px;
        }
        
        .card-modal-wrapper.active .modal-card {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
        
        /* Platzhalter-Bild mit fester Höhe */
        .card-placeholder-container {
            width: 100%;
            height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .card-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        
        /* Karteninhalt mit zentrierter Frage und Serifenschrift */
        .card-content {
            padding: 28px;
            background-color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }
        
        .card-question-text {
            font-family: 'Lora', serif; /* Elegante Serifenschrift */
            font-size: 17px;
            font-weight: 500;
            line-height: 1.6;
            color: #333;
            text-align: center; /* Zentrierte Frage */
        }
        
        /* UI-Elemente unterhalb der Karte */
        .draw-new-card {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 30px;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
            margin-top: 20px;
        }
        
        .draw-new-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
            background-color: rgba(255, 255, 255, 0.25);
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 32px;
            height: 32px;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: none;
            z-index: 10;
            transition: all 0.2s ease;
        }
        
        .close-modal:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1) rotate(90deg);
        }
        
        /* Hintergrund-Elemente */
        .background-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(124, 108, 216, 0.05);
            pointer-events: none;
        }
        
        .shape:nth-child(1) {
            width: 400px;
            height: 400px;
            top: -200px;
            right: -100px;
        }
        
        .shape:nth-child(2) {
            width: 300px;
            height: 300px;
            bottom: -150px;
            left: -50px;
        }
        
        .shape:nth-child(3) {
            width: 200px;
            height: 200px;
            top: 50%;
            left: 10%;
            transform: translateY(-50%);
        }
        
        /* Lade-Indikator */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 3px solid rgba(124, 108, 216, 0.3);
            border-radius: 50%;
            border-top-color: #7c6cd8;
            animation: spin 1s ease-in-out infinite;
            z-index: 1200;
            display: none;
        }
        
        @keyframes spin {
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        /* Card-Drawing Animation */
        @keyframes floatIn {
            0% { 
                transform: scale(0.6) translateY(40px) rotate(-5deg); 
                opacity: 0;
            }
            100% { 
                transform: scale(1) translateY(0) rotate(0deg); 
                opacity: 1;
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes float {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
        }
        
        .deck {
            animation: fadeIn 0.8s ease forwards, float 6s ease-in-out infinite;
        }
        
        .deck:nth-child(1) { animation-delay: 0.1s, 0s; }
        .deck:nth-child(2) { animation-delay: 0.2s, 0.5s; }
        .deck:nth-child(3) { animation-delay: 0.3s, 1s; }
        .deck:nth-child(4) { animation-delay: 0.4s, 1.5s; }
        .deck:nth-child(5) { animation-delay: 0.5s, 2s; }
        
        /* Responsive Anpassungen */
        @media (max-width: 768px) {
            .container {
                padding: 15px 10px 60px;
            }
            
            .card-decks {
                gap: 20px;
            }
            
            .deck {
                width: 160px;
            }
            
            .cards-container {
                height: 224px;
            }
            
            .main-title {
                font-size: 28px;
            }
            
            .modal-card {
                width: 300px;
            }
            
            .card-question-text {
                font-size: 16px;
            }
            
            .intro-text {
                font-size: 15px;
            }
            
            .draw-new-card {
                padding: 10px 20px;
                font-size: 14px;
            }
            
            .intro-section {
                padding: 25px 20px;
                margin-bottom: 30px;
            }
        }
        
        @media (max-width: 500px) {
            .container {
                padding: 10px 8px 70px;
            }
            
            .card-decks {
                gap: 12px;
            }
            
            .deck {
                width: 145px;
            }
            
            .cards-container {
                height: 203px;
            }
            
            .deck-title {
                font-size: 15px;
                margin-bottom: 12px;
            }
            
            .modal-card {
                width: 280px;
            }
            
            .card-question-text {
                font-size: 15px;
            }
            
            .main-title {
                font-size: 24px;
            }
            
            .draw-new-card {
                padding: 10px 18px;
                font-size: 14px;
            }
            
            .intro-section {
                padding: 20px 15px;
                margin-bottom: 25px;
            }
            
            .intro-text {
                font-size: 14px;
                padding: 0 5px;
            }
            
            .card-placeholder-container {
                height: 180px;
            }
            
            .card-content {
                padding: 22px;
                min-height: 180px;
            }
        }
        
        @media (max-width: 370px) {
            .card-decks {
                gap: 10px 5px;
            }
            
            .deck {
                width: 130px;
            }
            
            .cards-container {
                height: 182px;
            }
            
            .modal-card {
                width: 260px;
            }
            
            .deck-title {
                font-size: 14px;
                padding-bottom: 8px;
                margin-bottom: 10px;
            }
            
            .main-title {
                font-size: 22px;
            }
            
            .footer {
                font-size: 12px;
            }
            
            .card-question-text {
                font-size: 14px;
            }
            
            .card-placeholder-container {
                height: 160px;
            }
            
            .card-content {
                min-height: 160px;
            }
        }
    </style>
</head>

<body>
    <div class="background-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="loading-indicator" id="loader"></div>

    <header class="header">
        <a href="index.html" class="header-link">Jana Sophie Breitmar</a>
    </header>

    <div class="container">
        <div class="intro-section">
            <h1 class="main-title">Neue Harmonie: Kompatibilitäts-Kartenset</h1>
            <p class="intro-text">
                Kompatibilität ist der geheime Schlüssel, der Menschen langfristig harmonisch zusammenhält. Mit diesem Kartenset kannst du herausfinden, welche Kompatibilität dir wichtig ist und im gemeinsamen Gespräch mit einer anderen Person erarbeiten, wie gut ihr in verschiedenen Bereichen zueinander passt.
                <br><br>
                Wähle einen Kartenstapel und ziehe eine Karte, um eine tiefgehende Frage zu entdecken. Die Fragen bieten wertvolle Gesprächsansätze, die dir helfen können, dich selbst und andere besser zu verstehen.
            </p>
        </div>

        <div class="card-decks">
            <!-- Alle Themen Kartenstapel -->
            <div class="deck deck-all" data-category="all">
                <h2 class="deck-title">Alle Themen</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="alle-themen.png" alt="Alle Themen" class="card-cover-img">
                            </div>
                            <div class="deck-label">Alle Themen</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="alle-themen.png" alt="Alle Themen" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="alle-themen.png" alt="Alle Themen" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="alle-themen.png" alt="Alle Themen" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="alle-themen.png" alt="Alle Themen" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grundlegendes Kartenstapel -->
            <div class="deck deck-grundlegendes" data-category="grundlegendes">
                <h2 class="deck-title">Grundlegendes</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="grundlegendes.png" alt="Grundlegendes" class="card-cover-img">
                            </div>
                            <div class="deck-label">Grundlegendes</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="grundlegendes.png" alt="Grundlegendes" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="grundlegendes.png" alt="Grundlegendes" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="grundlegendes.png" alt="Grundlegendes" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="grundlegendes.png" alt="Grundlegendes" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Freundschaft Kartenstapel -->
            <div class="deck deck-freundschaft" data-category="freundschaft">
                <h2 class="deck-title">Freundschaft</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="freundschaft.png" alt="Freundschaft" class="card-cover-img">
                            </div>
                            <div class="deck-label">Freundschaft</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="freundschaft.png" alt="Freundschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="freundschaft.png" alt="Freundschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="freundschaft.png" alt="Freundschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="freundschaft.png" alt="Freundschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Partnerschaft Kartenstapel -->
            <div class="deck deck-partnerschaft" data-category="partnerschaft">
                <h2 class="deck-title">Partnerschaft</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="partnerschaft.png" alt="Partnerschaft" class="card-cover-img">
                            </div>
                            <div class="deck-label">Partnerschaft</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="partnerschaft.png" alt="Partnerschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="partnerschaft.png" alt="Partnerschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="partnerschaft.png" alt="Partnerschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="partnerschaft.png" alt="Partnerschaft" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sexualität Kartenstapel -->
            <div class="deck deck-sexualitaet" data-category="sexualitaet">
                <h2 class="deck-title">Sexualität</h2>
                <div class="deck-instruction">Tap zum Ziehen einer Karte</div>
                <div class="cards-container">
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="sexualitaet.png" alt="Sexualität" class="card-cover-img">
                            </div>
                            <div class="deck-label">Sexualität</div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="sexualitaet.png" alt="Sexualität" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="sexualitaet.png" alt="Sexualität" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="sexualitaet.png" alt="Sexualität" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                    <div class="card stacked">
                        <div class="card-back">
                            <div class="card-image-container">
                                <img src="sexualitaet.png" alt="Sexualität" class="card-cover-img">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karten-Modal mit UI-Elementen außerhalb -->
    <div class="card-modal-wrapper" id="card-modal-wrapper">
        <button class="close-modal" id="close-modal">&times;</button>
        
        <!-- Kategorie ÜBER dem Modal -->
        <span class="card-category-label" id="card-category-label"></span>
        
        <div class="modal-card">
            <div class="card-placeholder-container">
                <img src="grundlegendes.png" alt="" class="card-placeholder" id="card-placeholder">
            </div>
            <div class="card-content">
                <p class="card-question-text" id="card-question-text"></p>
            </div>
        </div>
        
        <!-- Button bleibt unten -->
        <button class="draw-new-card" id="draw-new-card">Neue Karte ziehen</button>
    </div>

    <footer class="footer">
        &copy; 2025 <a href="index.html?modal=ueber-mich">Jana Sophie Breitmar</a> | <a href="index.html?modal=whats-new">What's new?</a> | <a href="index.html?modal=impressum">Impressum</a>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Loader anzeigen
            const loader = document.getElementById('loader');
            loader.style.display = 'block';
            
            // Fragen und Platzhalter für jede Kategorie - Mit eindeutigen Bildpfaden
const questions = {
    grundlegendes: [
        {
            text: "Was ist deine höchste Vision im Leben?",
            placeholder: "fragekatalog/frage1.png"
        },
        {
            text: "Welche Werte sind dir wirklich wertvoll und wichtig im Leben?",
            placeholder: "fragekatalog/frage2.png"
        },
        {
            text: "Wie verhältst du dich in Streitsituationen (z. B. flüchten, erstarren, kämpfen, unterwerfen)?",
            placeholder: "fragekatalog/frage3.png"
        },
        {
            text: "Was ist deine Sprache der Liebe (z. B. Geschenke, liebevolle Botschaften, gemeinsame Zeit, körperliche Nähe, Gefälligkeiten und Unterstützung)?",
            placeholder: "fragekatalog/frage4.png"
        },
        {
            text: "Was schätzt du an dir selbst, was andere oft übersehen?",
            placeholder: "fragekatalog/frage5.png"
        },
        {
            text: "Wie offen gehst du mit deinen Gefühlen und mit Verletzlichkeit um?",
            placeholder: "fragekatalog/frage6.png"
        },
        {
            text: "Bist du eher impulsiv oder zurückhaltend-kontrolliert mit deinen Gefühlen?",
            placeholder: "fragekatalog/frage7.png"
        },
        {
            text: "Wie wichtig ist dir Kommunikation? Was bedeutet das für dich?",
            placeholder: "fragekatalog/frage8.png"
        },
        {
            text: "Was bringt dich im Leben am meisten zum Strahlen/Erblühen?",
            placeholder: "fragekatalog/frage9.png"
        },
        {
            text: "Was triggert dich am meisten? Wo sollte man dich besser nicht verletzen?",
            placeholder: "fragekatalog/frage10.png"
        }, 
        {
            text: "Was sind deine Interessen? Womit beschäftigst du dich am liebsten?",
            placeholder: "fragekatalog/frage11.png"
        },
        {
            text: "Bist du eher ein rationaler Kopfmensch oder ein intuitiver Gefühlsmensch? Oder jemand, der spontan und instinktiv aus dem Bauch heraus handelt?",
            placeholder: "fragekatalog/frage12.png"
        },
        {
            text: "Was sind deine Stärken und Schwächen?",
            placeholder: "fragekatalog/frage13.png"
        },
        {
            text: "Was lässt dich morgens aufstehen?",
            placeholder: "fragekatalog/frage14.png"
        },
        {
            text: "Wie wichtig ist dir persönliche Weiterentwicklung und wie setzt du sie um?",
            placeholder: "fragekatalog/frage15.png"
        },
        {
            text: "Lebst du ein authentisches Leben?",
            placeholder: "fragekatalog/frage16.png"
        },
        {
            text: "Gab es Momente in deinem Leben, die dich besonders geprägt haben?",
            placeholder: "fragekatalog/frage17.png"
        }
    ],
    freundschaft: [
        {
            text: "Auf welchen Lebensbereichen liegt dein Fokus (z. B. Arbeit, Partnerschaft, Familie, Freundschaft, Hobby)?",
            placeholder: "fragekatalog/frage18.png"
        },
        {
            text: "Bist du offen dafür, regelmäßig an der Beziehung zu arbeiten, um die Verbindung immer tiefer werden zu lassen, oder wünscht du dir eine möglichst leichte Beziehung?",
            placeholder: "fragekatalog/frage19.png"
        },
        {
            text: "Welche Bedeutung hat Spiritualität oder Glaube für dich?",
            placeholder: "fragekatalog/frage20.png"
        },
        {
            text: "Welche Träume möchtest du in deinem Leben erreichen (z. B. Kinder, bestimmter Erfolg im Beruf etc.)?",
            placeholder: "fragekatalog/frage21.png"
        },
        {
            text: "Gibt es etwas, das du niemals verzeihen könntest?",
            placeholder: "fragekatalog/frage22.png"
        },
        {
            text: "Was ist deine liebste Art, gemeinsam Zeit zu verbringen?",
            placeholder: "fragekatalog/frage23.png"
        },
        {
            text: "Welche Rolle spielen Bücher, Filme oder Musik in deinem Leben?",
            placeholder: "fragekatalog/frage24.png"
        },
        {
            text: "Welche Rolle spielt Reisen in deinem Leben?",
            placeholder: "fragekatalog/frage25.png"
        },
        {
            text: "Welche Herausforderungen beschäftigen dich im Moment am meisten?",
            placeholder: "fragekatalog/frage26.png"
        },
        {
            text: "Gibt es gesellschaftliche oder soziale Themen, die dir wichtig sind?",
            placeholder: "fragekatalog/frage27.png"
        },
        {
            text: "Wie wichtig ist dir regelmäßiger Kontakt und Austausch mit deinen Freunden?",
            placeholder: "fragekatalog/frage28.png"
        }
    ],
    partnerschaft: [
        {
            text: "Was ist deine größte Hoffnung an eine Beziehung?",
            placeholder: "fragekatalog/frage29.png"
        },
        {
            text: "Nach welchem Beziehungsmodell möchtest du leben (z. B. monogam, poly, offene Beziehung)?",
            placeholder: "fragekatalog/frage30.png"
        },
        {
            text: "Welche persönlichen Grenzen möchtest du in einer Beziehung respektiert wissen?",
            placeholder: "fragekatalog/frage31.png"
        },
        {
            text: "Gibt es etwas, das du in früheren Beziehungen vermisst hast und jetzt suchst?",
            placeholder: "fragekatalog/frage32.png"
        },
        {
            text: "Brauchst du eher viel Nähe oder viel Freiheit?",
            placeholder: "fragekatalog/frage33.png"
        },
        {
            text: "Welche Art von Freiheit brauchst du in einer Partnerschaft?",
            placeholder: "fragekatalog/frage34.png"
        },
        {
            text: "Welche unerfüllten Sehnsüchte, Wünsche oder Bedürfnisse hast du?",
            placeholder: "fragekatalog/frage35.png"
        },
        {
            text: "Wie ist deine Beziehung zu deinen Ex-Partnern oder Ex-Partnerinnen?",
            placeholder: "fragekatalog/frage36.png"
        },
        {
            text: "Wie ist deine Beziehung zu deinen Eltern und deiner Familie?",
            placeholder: "fragekatalog/frage37.png"
        },
        {
            text: "Wie wichtig ist dir der Kontakt zur Familie des Partners?",
            placeholder: "fragekatalog/frage38.png"
        },
        {
            text: "Wie gehst du mit Eifersucht um?",
            placeholder: "fragekatalog/frage39.png"
        },
        {
            text: "Was macht für dich eine tiefe Verbindung aus?",
            placeholder: "fragekatalog/frage40.png"
        },
        {
            text: "Was wäre für dich ein Zeichen dafür, dass wir langfristig nicht-kompatibel zueinander sind (z. B. unterschiedliche Werte, Streitverhalten, Kinderwunsch etc.)?",
            placeholder: "fragekatalog/frage41.png"
        },
        {
            text: "Welche Vorstellung hast du von einer idealen Partnerschaft?",
            placeholder: "fragekatalog/frage42.png"
        },
        {
            text: "Was wäre für dich das größte Geschenk, dass dir ein Partner machen kann?",
            placeholder: "fragekatalog/frage43.png"
        },
        {
            text: "Wie ist deine Beziehung zu Geld?",
            placeholder: "fragekatalog/frage44.png"
        },
        {
            text: "Welchen Umgang mit Geld bevorzugst du in einer Partnerschaft?",
            placeholder: "fragekatalog/frage45.png"
        },
        {
            text: "Fällt es dir leicht, dich zu verlieben bzw. dich für Liebe zu öffnen?",
            placeholder: "fragekatalog/frage46.png"
        },
        {
            text: "Bist du lieber viel unterwegs oder zu Hause eingekuschelt?",
            placeholder: "fragekatalog/frage47.png"
        },
        {
            text: "Gibt es eine gemeinsame Vision, die du dir für deine Partnerschaft wünschst?",
            placeholder: "fragekatalog/frage48.png"
        },
        {
            text: "Welche Art von Rückhalt wünschst du dir von deinem Partner?",
            placeholder: "fragekatalog/frage49.png"
        },
        {
            text: "Wann würdest du eine Beziehung beenden und wann darum kämpfen?",
            placeholder: "fragekatalog/frage50.png"
        },
        {
            text: "Oft verlieben wir uns zu Beginn nicht in die andere Person, sondern in die Wunschvorstellung eines Idealpartners, der endlich all unsere Sehnsüchte erfüllt. Inwiefern könnte das auch bei uns zutreffen? Wie sieht diese Idealvorstellung in deinem Kopf aus?",
            placeholder: "fragekatalog/frage51.png"
        },
        {
            text: "Inwiefern hast du schonmal zum Beginn der Beziehung bzw. in der Datingphase Dinge gemacht, um die andere Person zu erobern, die du langfristig in einer Beziehung dann nicht aufrecht erhalten kannst oder willst (z. B. Komplimente machen, besonders aufmerksam sein, besonders viel Zeit mit der Person verbringen)?",
            placeholder: "fragekatalog/frage52.png"
        },
        {
            text: "Welche unbewussten Erwartungen hast du an einen Partner, der dich liebt? Beende den Satz: \"Wenn du mich wirklich liebst, dann…\"",
            placeholder: "fragekatalog/frage53.png"
        },
        {
            text: "Wie stehst du zum Heiraten?",
            placeholder: "fragekatalog/frage54.png"
        }
    ],
    sexualitaet: [
        {
            text: "Was ist für dich wichtig, um eine erfüllte Sexualität zu leben?",
            placeholder: "fragekatalog/frage55.png"
        },
        {
            text: "Gibt es Aspekte in deiner Sexualität, die du noch erforschen möchtest?",
            placeholder: "fragekatalog/frage56.png"
        },
        {
            text: "Was sind für dich absolute Grenzen in der Sexualität?",
            placeholder: "fragekatalog/frage57.png"
        },
        {
            text: "Was bedeutet dir körperliche Nähe abseits von Sexualität?",
            placeholder: "fragekatalog/frage58.png"
        },
        {
            text: "Was wünscht du dir von deinem Partner, um dich sexuell sicher zu fühlen?",
            placeholder: "fragekatalog/frage59.png"
        },
        {
            text: "Welche Rolle spielt dein Körperbild in deinem sexuellen Erleben?",
            placeholder: "fragekatalog/frage60.png"
        },
        {
            text: "Wie wichtig ist dir gemeinsame Zeit vor oder nach dem Sex?",
            placeholder: "fragekatalog/frage61.png"
        },
        {
            text: "Zu welchem Geschlecht oder welchen Geschlechtern fühlst du dich hingezogen? Was ist deine sexuelle Orientierung?",
            placeholder: "fragekatalog/frage62.png"
        }, 
        {
            text: "Was magst du beim Sex gerne und was nicht?",
            placeholder: "fragekatalog/frage63.png"
        },
        {
            text: "Hast du gerne Sex?",
            placeholder: "fragekatalog/frage64.png"
        },
        {
            text: "Wie oft möchtest du Sex haben?",
            placeholder: "fragekatalog/frage65.png"
        }
    ]
};
            
            // DOM-Elemente
            const decks = document.querySelectorAll('.deck');
            const cardModalWrapper = document.getElementById('card-modal-wrapper');
            const closeModalBtn = document.getElementById('close-modal');
            const drawNewCardBtn = document.getElementById('draw-new-card');
            const cardPlaceholder = document.getElementById('card-placeholder');
            const cardCategoryLabel = document.getElementById('card-category-label');
            const cardQuestionText = document.getElementById('card-question-text');
            const modalCard = document.querySelector('.modal-card');
            
            // Variablen für aktive Karte
            let activeCategory = null;
            let currentDeck = null;
            
            // Kategorie-Labels
            const categoryLabels = {
                all: "Zufällige Frage",
                grundlegendes: "Grundlegendes",
                freundschaft: "Freundschaft",
                partnerschaft: "Partnerschaft",
                sexualitaet: "Sexualität"
            };
            
            // Funktion um eine zufällige Karte zu ziehen mit Animation
            function drawCard(category, deck) {
                // Lade-Animation anzeigen
                loader.style.display = 'block';
                
                // Aktive Kategorie und Deck speichern
                activeCategory = category;
                currentDeck = deck;
                
                // Deck-Position für Animation speichern
                let deckRect = null;
                if (deck) {
                    deckRect = deck.getBoundingClientRect();
                }
                
                // Fragen je nach Kategorie auswählen
                let questionItem;
                
                if (category === 'all') {
                    // Bei "Alle Themen" eine zufällige Kategorie auswählen
                    const categories = ['grundlegendes', 'freundschaft', 'partnerschaft', 'sexualitaet'];
                    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
                    const randomQuestions = questions[randomCategory];
                    questionItem = randomQuestions[Math.floor(Math.random() * randomQuestions.length)];
                } else {
                    // Andernfalls direkt aus der gewählten Kategorie
                    const categoryQuestions = questions[category];
                    questionItem = categoryQuestions[Math.floor(Math.random() * categoryQuestions.length)];
                }
                
                // Füge Kategorie-Klasse zum Modal hinzu
                cardModalWrapper.className = 'card-modal-wrapper ' + (category === 'all' ? 'all-category' : category + '-category');
                
                // Setze Karteninhalt
                cardCategoryLabel.textContent = categoryLabels[category];
                cardQuestionText.textContent = questionItem.text;
                cardPlaceholder.src = questionItem.placeholder;
                cardPlaceholder.alt = questionItem.text;
                
                // Öffne das Modal mit Animation
                cardModalWrapper.classList.add('active');
                
                // Karten-Zieh-Animation
                if (deckRect) {
                    // Setze die Modalcard für die Animation zurück
                    modalCard.style.animation = 'none';
                    modalCard.style.opacity = '0';
                    modalCard.style.transform = 'scale(0.6) translateY(40px) rotate(-5deg)';
                    
                    // Kleine Verzögerung für visuelle Effekte
                    setTimeout(() => {
                        // Animation starten
                        modalCard.style.animation = 'floatIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';
                    }, 100);
                }
                
                // Lade-Animation ausblenden
                loader.style.display = 'none';
            }
            
            // Event-Listener für Kartenstapel
            decks.forEach(deck => {
                deck.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    drawCard(category, this);
                });
            });
            
            // Event-Listener für Schließen-Button
            closeModalBtn.addEventListener('click', function() {
                cardModalWrapper.classList.remove('active');
                setTimeout(() => {
                    activeCategory = null;
                    currentDeck = null;
                }, 300);
            });
            
            // Event-Listener für Klick außerhalb des Modals
            cardModalWrapper.addEventListener('click', function(e) {
                if (e.target === this) {
                    cardModalWrapper.classList.remove('active');
                    setTimeout(() => {
                        activeCategory = null;
                        currentDeck = null;
                    }, 300);
                }
            });
            
            // Event-Listener für "Neue Karte ziehen"
            drawNewCardBtn.addEventListener('click', function() {
                if (activeCategory) {
                    // Für die neue Karte eine kleine Animation
                    modalCard.style.animation = 'none';
                    modalCard.offsetHeight; // Reflow
                    modalCard.style.animation = 'floatIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards';
                    
                    // Neue Karte aus der gleichen Kategorie ziehen
                    drawCard(activeCategory, currentDeck);
                }
            });
            
            // ESC-Taste zum Schließen
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && cardModalWrapper.classList.contains('active')) {
                    cardModalWrapper.classList.remove('active');
                    setTimeout(() => {
                        activeCategory = null;
                        currentDeck = null;
                    }, 300);
                }
            });
            
            // Bilder vorladen
            function preloadImages() {
                const imageUrls = [
                    'alle-themen.png',
                    'grundlegendes.png',
                    'freundschaft.png',
                    'partnerschaft.png',
                    'sexualitaet.png'
                ];
                
                // Wir laden keine weiteren Platzhalterbilder, da alle 'grundlegendes.png' verwenden
                
                const imagePromises = imageUrls.map(url => {
                    return new Promise((resolve, reject) => {
                        const img = new Image();
                        img.onload = () => resolve(url);
                        img.onerror = () => {
                            console.warn(`Konnte Bild nicht laden: ${url}`);
                            resolve(url); // Trotzdem auflösen, damit der Prozess weitergeht
                        };
                        img.src = url;
                    });
                });
                
                // Warten, bis Bilder geladen sind
                Promise.all(imagePromises)
                    .then(() => {
                        // Lade-Indikator ausblenden
                        loader.style.display = 'none';
                    })
                    .catch(err => {
                        console.error('Fehler beim Vorladen der Bilder:', err);
                        loader.style.display = 'none';
                    });
            }
            
            // Bilder vorladen
            preloadImages();
        });
    </script>
</body>

</html>