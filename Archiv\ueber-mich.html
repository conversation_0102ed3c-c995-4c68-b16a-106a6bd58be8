<!-- ueber-mich.hmtl -->
<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Über mich - <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Zusätzliche Styles für die Über-mich-Seite */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            background-color: #7c6cd8;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%23ffffff' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%23ffffff' opacity='0.1'%3E%3Ccircle cx='769' cy='229' r='5'/%3E%3Ccircle cx='539' cy='269' r='5'/%3E%3Ccircle cx='603' cy='493' r='5'/%3E%3Ccircle cx='731' cy='737' r='5'/%3E%3Ccircle cx='520' cy='660' r='5'/%3E%3Ccircle cx='309' cy='538' r='5'/%3E%3Ccircle cx='295' cy='764' r='5'/%3E%3Ccircle cx='40' cy='599' r='5'/%3E%3Ccircle cx='102' cy='382' r='5'/%3E%3Ccircle cx='127' cy='80' r='5'/%3E%3Ccircle cx='370' cy='105' r='5'/%3E%3Ccircle cx='578' cy='42' r='5'/%3E%3Ccircle cx='237' cy='261' r='5'/%3E%3Ccircle cx='390' cy='382' r='5'/%3E%3C/g%3E%3C/svg%3E");
            background-attachment: fixed;
        }
        
        .page-container {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .profile-container {
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .profile-content {
            padding: 30px;
            overflow-y: auto;
            max-height: calc(80vh - 60px);
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        /* Scrollbar Styling für Webkit-Browser (Chrome, Safari) */
        .profile-content::-webkit-scrollbar {
            width: 8px;
        }

        .profile-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .profile-content::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .profile-content::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
        }

        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
            text-align: center;
        }

        .profile-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 20px;
            border: 3px solid #7c6cd8;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .profile-name {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }

        .profile-title {
            font-family: 'Caveat', cursive;
            font-size: 20px;
            color: #7c6cd8;
            margin-bottom: 15px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 20px;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            color: #333;
        }

        .section p {
            line-height: 1.7;
            margin-bottom: 15px;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: #444;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .social-link {
            color: #7c6cd8;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .social-link:hover {
            color: #6658b8;
            text-decoration: underline;
        }

        .back-link {
            display: inline-block;
            margin-top: 30px;
            color: #7c6cd8;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            color: #6658b8;
            text-decoration: underline;
        }

        .quote {
            font-style: italic;
            color: #666;
            padding: 15px;
            border-left: 3px solid #7c6cd8;
            margin: 20px 0;
            background-color: rgba(124, 108, 216, 0.05);
        }
    </style>
</head>

<body>
    <header class="header">
        <a href="index.html" class="header-link">Jana Sophie Breitmar</a>
    </header>

    <div class="page-container">
        <div class="profile-container">
            <div class="profile-content">
                <div class="profile-header">
                    <img src="/api/placeholder/300/300" alt="Jana Sophie Breitmar" class="profile-image">
                    <h1 class="profile-name">Jana Sophie Breitmar</h1>
                    <div class="profile-title">Autorin, Beraterin & Musikerin</div>
                </div>

                <div class="section">
                    <h2 class="section-title">Mein Weg</h2>
                    <p>Nach meinem Studium in Psychologie und Kommunikation habe ich mich auf die Erforschung zwischenmenschlicher Beziehungen und persönlicher Entwicklung spezialisiert. Mein beruflicher Werdegang führte mich durch verschiedene Stationen in der Unternehmensberatung, bevor ich mich entschied, mein Wissen und meine Erfahrungen auf eine persönlichere Art zu teilen.</p>
                    <p>Heute widme ich mich der Erforschung und Vermittlung transformativer Konzepte, die Menschen helfen, tiefere Verbindungen zu sich selbst, zu anderen und zur Welt aufzubauen. Meine Arbeit ist geprägt von der Überzeugung, dass echte Veränderung in unserer Gesellschaft nur durch bewusstere Beziehungen entstehen kann.</p>
                </div>

                <div class="quote">
                    "Der Weg zu einer besseren Welt beginnt mit der Beziehung zu uns selbst. Erst wenn wir uns selbst wirklich kennen und annehmen, können wir echte Verbindungen zu anderen aufbauen und gemeinsam wachsen."
                </div>

                <div class="section">
                    <h2 class="section-title">Meine Vision</h2>
                    <p>Ich möchte eine Welt mitgestalten, in der Menschen in tiefem Kontakt mit sich selbst und miteinander leben. Eine Welt, in der wir unsere Unterschiede als Bereicherung sehen und gemeinsam Lösungen für die Herausforderungen unserer Zeit finden.</p>
                    <p>Mit meiner Arbeit möchte ich Brücken bauen – zwischen Verstand und Gefühl, zwischen persönlichem Wachstum und gesellschaftlicher Verantwortung, zwischen alter Weisheit und neuen Erkenntnissen. Ich glaube fest daran, dass wir durch bewusstere Beziehungen und authentische Kommunikation den Weg zu mehr Frieden und Nachhaltigkeit ebnen können.</p>
                </div>

                <div class="section">
                    <h2 class="section-title">Meine Arbeit</h2>
                    <p>Meine Arbeit umfasst verschiedene Bereiche:</p>
                    <p><strong>Beratung und Begleitung:</strong> In Einzelsettings und Workshops unterstütze ich Menschen dabei, ihre Beziehungen zu vertiefen und Konflikte als Chance für Wachstum zu nutzen.</p>
                    <p><strong>Schreiben:</strong> Durch Artikel, Bücher und andere Publikationen teile ich Erkenntnisse und praktische Werkzeuge für persönliches Wachstum und bewusstere Beziehungen.</p>
                    <p><strong>Musik:</strong> Als Musikerin drücke ich Emotionen und tiefe Wahrheiten aus, die manchmal nur durch Klänge vermittelt werden können. Meine Lieder sind eine Einladung, tiefer zu fühlen und sich zu verbinden.</p>
                </div>

                <div class="section">
                    <h2 class="section-title">Kontakt</h2>
                    <p>Ich freue mich über Nachrichten, Fragen und Anregungen. Du erreichst mich unter:</p>
                    <p>E-Mail: <EMAIL></p>

                    <div class="social-links">
                        <a href="#" class="social-link">Instagram</a>
                        <a href="#" class="social-link">YouTube</a>
                        <a href="#" class="social-link">Spotify</a>
                    </div>
                </div>

                <a href="index.html" class="back-link">&larr; Zurück zur Startseite</a>
            </div>
        </div>
    </div>

    <footer class="footer">
        &copy; 2025 <a href="ueber-mich.html">Jana Sophie Breitmar</a> | <a href="impressum.html">Impressum</a>
    </footer>
</body>

</html>