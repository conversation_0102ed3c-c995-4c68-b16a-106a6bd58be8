<!-- impressum.html -->
<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Impressum - <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&family=Montserrat:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Zusätzliche Styles für die Impressum-Seite */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            background-color: #7c6cd8;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%23ffffff' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%23ffffff' opacity='0.1'%3E%3Ccircle cx='769' cy='229' r='5'/%3E%3Ccircle cx='539' cy='269' r='5'/%3E%3Ccircle cx='603' cy='493' r='5'/%3E%3Ccircle cx='731' cy='737' r='5'/%3E%3Ccircle cx='520' cy='660' r='5'/%3E%3Ccircle cx='309' cy='538' r='5'/%3E%3Ccircle cx='295' cy='764' r='5'/%3E%3Ccircle cx='40' cy='599' r='5'/%3E%3Ccircle cx='102' cy='382' r='5'/%3E%3Ccircle cx='127' cy='80' r='5'/%3E%3Ccircle cx='370' cy='105' r='5'/%3E%3Ccircle cx='578' cy='42' r='5'/%3E%3Ccircle cx='237' cy='261' r='5'/%3E%3Ccircle cx='390' cy='382' r='5'/%3E%3C/g%3E%3C/svg%3E");
            background-attachment: fixed;
        }
        
        .page-container {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }

        .content-container {
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .content-scroll {
            padding: 30px;
            overflow-y: auto;
            max-height: calc(80vh - 60px);
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        /* Scrollbar Styling für Webkit-Browser (Chrome, Safari) */
        .content-scroll::-webkit-scrollbar {
            width: 8px;
        }

        .content-scroll::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        .content-scroll::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .content-scroll::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 24px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #333;
        }

        .section p, .section li {
            line-height: 1.6;
            margin-bottom: 15px;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: #444;
        }

        .contact-info {
            background-color: transparent;
            padding: 0;
            margin-bottom: 30px;
        }

        .contact-info p {
            font-size: 16px;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            text-align: left;
        }

        .back-link {
            display: inline-block;
            margin-top: 30px;
            color: #7c6cd8;
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            color: #6658b8;
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <header class="header">
        <a href="index.html" class="header-link">Jana Sophie Breitmar</a>
    </header>

    <div class="page-container">
        <div class="content-container">
            <div class="content-scroll">
                <!-- Impressum Sektion -->
                <div class="section">
                    <h1 class="section-title">Impressum</h1>
                    <p><strong>Angaben gemäß § 5 TMG:</strong></p>
                    <p>Jana Sophie Breitmar<br>
                       Musterstraße 123<br>
                       12345 Musterstadt<br>
                       Deutschland</p>

                    <p><strong>Kontakt:</strong><br>
                       E-Mail: <EMAIL></p>

                    <p><strong>Verantwortlich für den Inhalt nach § 55 Abs. 2 RStV:</strong><br>
                       Jana Sophie Breitmar<br>
                       Musterstraße 123<br>
                       12345 Musterstadt</p>
                </div>

                <!-- Datenschutz Sektion -->
                <div class="section">
                    <h1 class="section-title">Datenschutzerklärung</h1>
                    
                    <p><strong>Allgemeiner Hinweis</strong></p>
                    <p>Ich sammle keinerlei personenbezogene Daten der Nutzer dieser Website. Es werden keine Cookies gesetzt, keine Analysedienste eingesetzt und keine Nutzerdaten gespeichert.</p>
                    
                    <p><strong>Hosting und Serverprotokolle</strong></p>
                    <p>Diese Website wird bei einem externen Dienstleister gehostet. Beim Besuch der Website werden automatisch durch den genutzten Webserver standardmäßig Informationen wie IP-Adresse, Browsertyp und -version sowie Datum und Uhrzeit des Zugriffs in Protokolldateien gespeichert. Diese Daten dienen ausschließlich zur Sicherstellung eines störungsfreien Betriebs der Website und werden nicht mit anderen Datenquellen zusammengeführt oder anderweitig ausgewertet.</p>
                    
                    <p><strong>Kontaktaufnahme</strong></p>
                    <p>Bei der Kontaktaufnahme per E-Mail werden die mitgeteilten Daten (E-Mail-Adresse, Name, Nachrichteninhalt) ausschließlich zur Beantwortung der Anfrage gespeichert und verwendet. Sie werden nicht an Dritte weitergegeben und nach Abschluss der Anfrage gelöscht, sofern keine gesetzlichen Aufbewahrungspflichten bestehen.</p>
                    
                    <p><strong>Rechte der betroffenen Personen</strong></p>
                    <p>Sie haben das Recht auf Auskunft, Berichtigung, Löschung und Einschränkung der Verarbeitung Ihrer personenbezogenen Daten. Bei Fragen zum Datenschutz können Sie sich jederzeit unter der oben genannten E-Mail-Adresse an mich wenden.</p>
                </div>

                <a href="index.html" class="back-link">&larr; Zurück zur Startseite</a>
            </div>
        </div>
    </div>

    <footer class="footer">
        &copy; 2025 <a href="ueber-mich.html">Jana Sophie Breitmar</a> | <a href="impressum.html">Impressum</a>
    </footer>
</body>

</html>