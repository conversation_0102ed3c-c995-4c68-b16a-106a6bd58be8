/* Schriftarten einbinden */
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 300;
    src: url('https://fonts.gstatic.com/s/montserrat/v29/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('https://fonts.gstatic.com/s/montserrat/v29/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('https://fonts.gstatic.com/s/montserrat/v29/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 700;
    src: url('https://fonts.gstatic.com/s/montserrat/v29/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Caveat';
    font-style: normal;
    font-weight: 400;
    src: url('https://fonts.gstatic.com/s/caveat/v18/WnznHAc5bAfYB2QRah7pcpNvOx-pjfJ9SII.woff2') format('woff2');
}
@font-face {
    font-display: swap;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: url('https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0C4i.woff2') format('woff2');
}

/* Grundlegende Styles */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --purple: #7c6cd8;
    --purple-light: #9389e2;
    --purple-dark: #6658b8;
    --dark-gray: #222222;
    --light-gray: #f5f5f5;
    --text-color: #444;
    --header-height: 40px;
    --footer-height: 40px;
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    font-family: 'Open Sans', Arial, sans-serif;
    color: var(--text-color);
}

body {
    display: flex;
    flex-direction: column;
    background-color: var(--light-gray);
}

/* Header Styling */
.header {
    height: var(--header-height);
    background-color: var(--dark-gray);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.header-link {
    color: var(--purple);
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.header-subtitle {
    font-family: 'Caveat', cursive;
    font-weight: normal;
    font-style: italic;
    text-transform: none;
    margin-left: 5px;
}

/* Main Content Styling */
.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.retreat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    width: 100%;
    height: 100%;
    gap: 1px;
    background-color: var(--dark-gray);
}

/* Retreat Card Styling */
.retreat-card {
    width: 100%;
    height: 100%;
    perspective: 1000px;
    overflow: hidden;
    position: relative;
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.retreat-card:hover .card-inner {
    transform: rotateY(10deg);
}

.retreat-card.flipped .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.card-front {
    background-color: var(--light-gray);
}

.card-back {
    background-color: var(--dark-gray);
    color: white;
    transform: rotateY(180deg);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow-y: auto;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-size: cover;
    background-position: center;
    position: relative;
}

.card-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0.1), rgba(0,0,0,0.6));
}

.card-title {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    padding: 15px;
    color: white;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1.5rem;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8);
    z-index: 2;
}

.card-content {
    max-width: 600px;
    padding: 10px;
}

.card-content h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: var(--purple-light);
    margin-bottom: 15px;
    text-align: center;
}

.card-content p {
    font-family: 'Open Sans', Arial, sans-serif;
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Footer Styling */
.footer {
    height: var(--footer-height);
    background-color: var(--dark-gray);
    color: var(--purple);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    font-size: 14px;
    z-index: 100;
}

.footer a {
    color: var(--purple);
    text-decoration: none;
    margin: 0 10px;
}

.footer a:hover {
    text-decoration: underline;
}

/* Modal Styling */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 200;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: 10px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s;
}

.modal.active .modal-content {
    transform: scale(1);
    opacity: 1;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    z-index: 210;
}

.content-scroll {
    padding: 30px;
    overflow-y: auto;
    max-height: 80vh;
    -webkit-overflow-scrolling: touch;
}

/* Profile Styling */
.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    text-align: center;
}

.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 3px solid var(--purple);
}

.profile-name {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--dark-gray);
}

.profile-title {
    font-family: 'Caveat', cursive;
    font-size: 22px;
    color: var(--purple);
    margin-bottom: 15px;
}

.quote {
    font-style: italic;
    color: var(--dark-gray);
    padding: 15px;
    border-left: 3px solid var(--purple);
    margin: 20px 0;
    background-color: rgba(124, 108, 216, 0.05);
}

.section {
    margin-bottom: 30px;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    color: var(--dark-gray);
}

.social-links {
    display: flex;
    gap: 20px;
}

.social-link {
    color: var(--purple);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s;
}

.social-link:hover {
    color: var(--purple-dark);
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .retreat-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 1fr);
    }
    
    .card-title {
        font-size: 1.2rem;
        bottom: 20px;
    }
    
    .header-link {
        font-size: 16px;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .content-scroll {
        padding: 20px;
    }
    
    .profile-image {
        width: 100px;
        height: 100px;
    }
    
    .profile-name {
        font-size: 20px;
    }
    
    .profile-title {
        font-size: 18px;
    }
    
    .section-title {
        font-size: 18px;
    }
}

/* Spezielle Styles für Mobilgeräte im Querformat */
@media (max-width: 896px) and (orientation: landscape) {
    .retreat-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }
    
    .header {
        height: 30px;
    }
    
    .footer {
        height: 30px;
    }
    
    .main-content {
        height: calc(100% - 60px);
    }
}

/* Hover-Effekte für bessere Interaktion */
.retreat-card .card-inner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(124, 108, 216, 0);
    transition: background-color 0.3s;
    z-index: 1;
    pointer-events: none;
}

.retreat-card:hover .card-inner::after {
    background-color: rgba(124, 108, 216, 0.1);
}

/* Cursor-Stil für klickbare Karten */
.retreat-card {
    cursor: pointer;
}

/* Animation für Hover-Effekt */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(124, 108, 216, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(124, 108, 216, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(124, 108, 216, 0);
    }
}

.retreat-card:hover {
    animation: pulse 1.5s infinite;
}

.retreat-card.flipped:hover {
    animation: none;
}