// cookie-banner.js - <PERSON><PERSON>-<PERSON> Funktionalität für die Website

document.addEventListener('DOMContentLoaded', function() {
    // Cookie-Banner erstellen und einfügen
    function createCookieBanner() {
        // Banner-Container erstellen
        const cookieBanner = document.createElement('div');
        cookieBanner.id = 'cookie-banner';
        cookieBanner.setAttribute('role', 'alert');
        cookieBanner.setAttribute('aria-live', 'polite');
        
        // Banner-Inhalt erstellen
        cookieBanner.innerHTML = `
            <div class="cookie-content">
                <p>Diese Website verwendet nur technisch notwendige Cookies, die für den Betrieb der Website erforderlich sind.</p>
                <button id="accept-cookies" tabindex="0" aria-label="Cookie-Hinweis akzeptieren">OK</button>
            </div>
        `;
        
        // CSS-Styles direkt einfügen
        const cookieStyles = document.createElement('style');
        cookieStyles.textContent = `
            #cookie-banner {
                position: fixed;
                bottom: 20px;
                left: 20px;
                max-width: 250px;
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                opacity: 1;
                transform: translateY(0);
                transition: opacity 0.5s ease, transform 0.5s ease;
                font-family: 'Open Sans', Arial, sans-serif;
            }
            
            #cookie-banner.hidden {
                opacity: 0;
                transform: translateY(20px);
                pointer-events: none;
            }
            
            #cookie-banner .cookie-content {
                padding: 12px;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            }
            
            #cookie-banner p {
                margin: 0 0 10px 0;
                font-size: 12px;
                line-height: 1.4;
                color: #444;
                font-family: 'Open Sans', Arial, sans-serif;
            }
            
            #cookie-banner button {
                background-color: #7c6cd8;
                color: white;
                border: none;
                padding: 5px 14px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 13px;
                font-weight: 500;
                transition: background-color 0.3s;
                align-self: flex-end;
                font-family: 'Open Sans', Arial, sans-serif;
            }
            
            #cookie-banner button:hover,
            #cookie-banner button:focus {
                background-color: #6658b8;
                outline: none;
            }

            #cookie-banner button:focus-visible {
                outline: 2px solid #7c6cd8;
                outline-offset: 2px;
            }
            
            @media (max-width: 768px) {
                #cookie-banner {
                    bottom: 10px;
                    left: 10px;
                    max-width: 220px;
                }
                
                #cookie-banner p {
                    font-size: 11px;
                }
            }
        `;
        
        // Styles und Banner zum Dokument hinzufügen
        document.head.appendChild(cookieStyles);
        document.body.appendChild(cookieBanner);
        
        return cookieBanner;
    }
    
    // Cookie-Funktionen
    function setCookie(name, value, days) {
        let expires = "";
        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/; SameSite=Strict";
    }
    
    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
    
    // Cookie-Banner-Funktionalität initialisieren
    function initCookieBanner() {
        // Prüfen, ob Cookie bereits gesetzt ist
        if (getCookie('cookie-consent')) {
            return; // Banner nicht anzeigen, wenn Consent bereits gegeben wurde
        }
        
        // Cookie-Banner erstellen
        const cookieBanner = createCookieBanner();
        const acceptButton = document.getElementById('accept-cookies');
        
        // Event-Listener für den Button
        acceptButton.addEventListener('click', function() {
            setCookie('cookie-consent', 'accepted', 365); // Consent für 1 Jahr speichern
            cookieBanner.classList.add('hidden');
            
            // Nach der Animation das Banner entfernen
            setTimeout(() => {
                cookieBanner.remove();
            }, 500);
        });

        // Keyboard Support
        acceptButton.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                acceptButton.click();
            }
        });
    }
    
    // Cookie-Banner initialisieren
    initCookieBanner();
});