// wertekompass.js - JavaScript-Funktionalität für den überarbeiteten Wertekompass

document.addEventListener('DOMContentLoaded', function() {
    // Liste der Wertebegriffe
    const valuesList = [
    "Abenteuer", "Achtsamkeit", "Akzeptanz", "Alchemie", "Annahme", 
    "Andersartigkeit", "Anerkennung", "Authentizität", "Autonomie", "Ausgleich",
    "Balance", "Begeisterung", "Besonnenheit", "Beständigkeit", "Bildung", 
    "Demut", "Diversität", "Disziplin", 
    "Effektivität", "Ehrlichkeit", "Einfachheit", "Einssein", "Empathie",
    "Entdeckung", "Erleuchtung", "Erfolg", 
    "Fairness", "Familie", "Flexibilität", "Fließen",
    "Freude", "Freundschaft", "Frieden", "Freiheit", "Fruchtbarkeit", "Fülle", "Fürsorge",
    "Geduld", "Gemeinschaft", "Genuss", "Gerechtigkeit", "Gesundheit",
    "Gleichheit", "Glück", "Göttlichkeit", "Großzügigkeit", 
    "Harmonie", "Heilige Rebellion", "Heiligkeit", "Hilfsbereitschaft",
    "Hingabe", "Hoffnung", "Humor", 
    "Individualität", "Innovation", "Inspiration", "Integrität", "Intelligenz", 
    "Intuition", "Innere Wahrheit",
    "Klarheit", "Kosmische Verbindung", "Kraft", "Kreativität", 
    "Lebendigkeit", "Leichtigkeit", "Leidenschaft", "Lernen", "Liebe", "Loyalität", 
    "Magie", "Männlichkeit", "Mitgefühl", "Mut", 
    "Nachhaltigkeit", "Naturverbundenheit", "Neugier", 
    "Offenheit", "Ordnung", "Optimismus", 
    "Potenzialentfaltung", "Präsenz", 
    "Respekt", "Ruhe", 
    "Sanftmut", "Schönheit", "Schöpfungskraft", "Sein und Werden", "Selbstbestimmung", 
    "Selbstliebe", "Selbstreflexion", "Selbstsicherheit", "Selbstvertrauen", "Sensibilität", 
    "Sicherheit", "Sinnlichkeit", "Souveränität", "Spaß", "Spiel", "Spiritualität", "Spontaneität", 
    "Standfestigkeit", "Stärke", "Stetigkeit", "Stille", 
    "Toleranz", "Tradition", "Transzendenz", "Treue", 
    "Umweltbewusstsein", "Unabhängigkeit", "Unkonventionalität", "Urvertrauen",
    "Verbundenheit", "Vergebung", "Verkörperung", "Verlässlichkeit", "Verspieltheit", "Vertrauen", "Vielfalt", 
    "Wachstum", "Wahrheit", "Wahrhaftigkeit", "Weiblichkeit", "Weichheit", "Weisheit", "Wertschätzung", "Wohlstand", 
    "Zufriedenheit", "Zusammenhalt", "Zuversicht"
    ];
    
    // Counter für benutzerdefinierte Werte
    let customValueCounter = 0;
    
    // Speicher für ausgewählte Werte
    let selectedValues = []; // Schritt 1: Alle ausgewählten Werte
    let familyValues = [];   // Schritt 2: Ausgewählte Familienwerte
    let personalValues = []; // Schritt 3: Ausgewählte persönliche Werte
    let top10Values = [];    // Schritt 4: Top 10 Werte
    let top3Values = [];     // Schritt 5: Top 3 Werte
    let topValue = "";       // Schritt 6: Der höchste Wert
    
    // Werte-Definitionen
    let valueDefinitions = {};
    
    // ----- Initialisierung -----
    
    // Wertebegriffe in Schritt 1 einfügen
    function initValuesList() {
        const valuesGrid = document.getElementById('step1-values');
        let html = '';
    
        // Alphabetisch sortieren
        valuesList.sort();
    
        valuesList.forEach(value => {
            html += `
                <div class="value-item" data-value="${value}">
                    <input type="checkbox" class="value-checkbox" id="value-${value}" data-value="${value}">
                    <label class="value-label" for="value-${value}">${value}</label>
                </div>
            `;
        });
    
        // Leere Zeile für benutzerdefinierte Werte hinzufügen
        html += `
            <div class="value-item custom" data-value="custom-${customValueCounter}">
                <input type="checkbox" class="value-checkbox" id="value-custom-${customValueCounter}" data-value="">
                <input type="text" class="value-input" placeholder="Eigener Wert..." data-id="${customValueCounter}">
            </div>
        `;
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl
        const valueItems = document.querySelectorAll('#step1-values .value-item:not(.custom)');
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.value-checkbox');
                const isCheckbox = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf die Checkbox geklickt wurde, ändere Checkbox-Status
                if (!isCheckbox) {
                    checkbox.checked = !checkbox.checked;
                }
                
                // Stil der Box anpassen
                if (checkbox.checked) {
                    this.classList.add('selected');
                } else {
                    this.classList.remove('selected');
                }
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    
        // Event-Listener für benutzerdefinierte Werte-Eingabefelder
        setupCustomValueInputs();
    }
    
    // Event-Listener für benutzerdefinierte Werte-Eingabefelder
    function setupCustomValueInputs() {
        const customInputs = document.querySelectorAll('.value-input');
    
        customInputs.forEach(input => {
            // Checkbox automatisch aktivieren, wenn Text eingegeben wird
            input.addEventListener('input', function() {
                const valueItem = this.closest('.value-item');
                const checkbox = valueItem.querySelector('.value-checkbox');
                
                // Checkbox aktualisieren
                if (this.value.trim() !== '') {
                    checkbox.checked = true;
                    valueItem.classList.add('selected');
                    checkbox.setAttribute('data-value', this.value.trim());
                } else {
                    checkbox.checked = false;
                    valueItem.classList.remove('selected');
                    checkbox.setAttribute('data-value', '');
                }
                
                // Neue leere Zeile hinzufügen, wenn etwas eingegeben wurde
                if (this.value.trim() !== '' && !valueItem.classList.contains('filled')) {
                    valueItem.classList.add('filled');
                    addNewCustomValueInput(valueItem.parentElement);
                }
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
            
            // Checkbox aktivieren/deaktivieren bei Klick
            const valueItem = input.closest('.value-item');
            const checkbox = valueItem.querySelector('.value-checkbox');
            
            valueItem.addEventListener('click', function(e) {
                // Nur reagieren, wenn nicht auf das Eingabefeld geklickt wurde
                if (!e.target.classList.contains('value-input')) {
                    // Prüfen, ob der Input einen Wert hat
                    if (input.value.trim() !== '') {
                        checkbox.checked = !checkbox.checked;
                        
                        if (checkbox.checked) {
                            this.classList.add('selected');
                        } else {
                            this.classList.remove('selected');
                        }
                        
                        // Prüfen, ob weiter geklickt werden kann
                        updateNextButtonState();
                    }
                }
            });
        });
    }
    
    // Neue leere Zeile für benutzerdefinierte Werte hinzufügen
    function addNewCustomValueInput(container) {
        customValueCounter++;
    
        const newCustomValue = document.createElement('div');
        newCustomValue.className = 'value-item custom';
        newCustomValue.setAttribute('data-value', `custom-${customValueCounter}`);
    
        newCustomValue.innerHTML = `
            <input type="checkbox" class="value-checkbox" id="value-custom-${customValueCounter}" data-value="">
            <input type="text" class="value-input" placeholder="Eigener Wert..." data-id="${customValueCounter}">
        `;
    
        container.appendChild(newCustomValue);
    
        // Event-Listener für das neue Element hinzufügen
        const input = newCustomValue.querySelector('.value-input');
    
        // Checkbox automatisch aktivieren, wenn Text eingegeben wird
        input.addEventListener('input', function() {
            const valueItem = this.closest('.value-item');
            const checkbox = valueItem.querySelector('.value-checkbox');
            
            // Checkbox aktualisieren
            if (this.value.trim() !== '') {
                checkbox.checked = true;
                valueItem.classList.add('selected');
                checkbox.setAttribute('data-value', this.value.trim());
            } else {
                checkbox.checked = false;
                valueItem.classList.remove('selected');
                checkbox.setAttribute('data-value', '');
            }
            
            // Neue leere Zeile hinzufügen, wenn etwas eingegeben wurde
            if (this.value.trim() !== '' && !valueItem.classList.contains('filled')) {
                valueItem.classList.add('filled');
                addNewCustomValueInput(valueItem.parentElement);
            }
            
            // Prüfen, ob weiter geklickt werden kann
            updateNextButtonState();
        });
    
        // Checkbox aktivieren/deaktivieren bei Klick
        const valueItem = input.closest('.value-item');
        const checkbox = valueItem.querySelector('.value-checkbox');
    
        valueItem.addEventListener('click', function(e) {
            // Nur reagieren, wenn nicht auf das Eingabefeld geklickt wurde
            if (!e.target.classList.contains('value-input')) {
                // Prüfen, ob der Input einen Wert hat
                if (input.value.trim() !== '') {
                    checkbox.checked = !checkbox.checked;
                    
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                    
                    // Prüfen, ob weiter geklickt werden kann
                    updateNextButtonState();
                }
            }
        });
    }
    
    // ----- Struktur und Navigation -----
    
    // Hauptabschnitte und Zeitlinien-Navigation initialisieren
    function initNavigation() {
        const mainSectionButtons = document.querySelectorAll('.main-section-btn');
        const timelineSections = document.querySelectorAll('.timeline-section');
        const timelineButtons = document.querySelectorAll('.timeline-button');
        const steps = document.querySelectorAll('.step-container');
        const nextButtons = document.querySelectorAll('.nav-btn.next');
        const backButtons = document.querySelectorAll('.nav-btn.back');
    
        // Aktiver Hauptabschnitt und Schritt
        let currentMainSection = 'erkunden';
        let currentStep = 1;
    
        // Hauptabschnitte-Button Click-Handler
        mainSectionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetSection = this.getAttribute('data-section');
                
                // UI aktualisieren
                mainSectionButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Zeitlinien-Abschnitte aktualisieren
                timelineSections.forEach(section => {
                    const sectionType = section.getAttribute('data-section');
                    if (sectionType === targetSection) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
                
                // Zum ersten Schritt des Abschnitts navigieren
                let firstStepInSection;
                switch(targetSection) {
                    case 'erkunden':
                        firstStepInSection = 1;
                        break;
                    case 'feinabstimmung':
                        firstStepInSection = 8;
                        break;
                    case 'leben':
                        firstStepInSection = 10;
                        break;
                }
                
                navigateToStep(firstStepInSection);
                currentMainSection = targetSection;
            });
        });
    
        // Zeitlinien-Button Click-Handler
        timelineButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetStep = parseInt(this.getAttribute('data-step'));
                
                // Nur zu bereits freigeschalteten Schritten navigieren
                const timelineItem = this.closest('.timeline-item');
                if (timelineItem.classList.contains('completed') || parseInt(this.getAttribute('data-step')) === currentStep) {
                    navigateToStep(targetStep);
                }
            });
        });
    
        // Weiter-Button Click-Handler
        nextButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Sammle ausgewählte Werte für den aktuellen Schritt
                collectValuesForStep(currentStep);
                
                // Markiere aktuellen Schritt als abgeschlossen
                const currentTimelineItem = document.querySelector(`.timeline-item[data-step="${currentStep}"]`);
                if (currentTimelineItem) {
                    currentTimelineItem.classList.add('completed');
                }
                
                // Spezielle Navigation für Übergänge zwischen Hauptabschnitten
                if (currentStep === 7) {
                    // Wechsel zu Feinabstimmung
                    const feinabstimmungButton = document.querySelector('.main-section-btn[data-section="feinabstimmung"]');
                    if (feinabstimmungButton) {
                        feinabstimmungButton.click();
                    }
                    return;
                } else if (currentStep === 9) {
                    // Wechsel zu Werte Leben
                    const lebenButton = document.querySelector('.main-section-btn[data-section="leben"]');
                    if (lebenButton) {
                        lebenButton.click();
                    }
                    return;
                }
                
                // Navigiere zum nächsten Schritt
                navigateToStep(currentStep + 1);
            });
        });
    
        // Zurück-Button Click-Handler
        backButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Spezielle Navigation für Übergänge zwischen Hauptabschnitten
                if (currentStep === 8) {
                    // Zurück zu Werte Erkunden
                    const erkundenButton = document.querySelector('.main-section-btn[data-section="erkunden"]');
                    if (erkundenButton) {
                        erkundenButton.click();
                    }
                    return;
                } else if (currentStep === 10) {
                    // Zurück zu Feinabstimmung
                    const feinabstimmungButton = document.querySelector('.main-section-btn[data-section="feinabstimmung"]');
                    if (feinabstimmungButton) {
                        feinabstimmungButton.click();
                    }
                    return;
                }
                
                // Navigiere zum vorherigen Schritt
                navigateToStep(currentStep - 1);
            });
        });
    
        // Navigation zu einem bestimmten Schritt
        function navigateToStep(stepNumber) {
            // Aktuellen Schritt aktualisieren
            currentStep = stepNumber;
            
            // Aktiven Schritt in der Zeitleiste aktualisieren
            const timelineItems = document.querySelectorAll('.timeline-item');
            timelineItems.forEach(item => {
                const itemStep = parseInt(item.getAttribute('data-step'));
                item.classList.remove('active');
                
                if (itemStep === currentStep) {
                    item.classList.add('active');
                }
            });
            
            // Aktives Hauptsegment basierend auf Schrittnummer ermitteln
            let targetMainSection;
            if (currentStep >= 1 && currentStep <= 7) {
                targetMainSection = 'erkunden';
            } else if (currentStep >= 8 && currentStep <= 9) {
                targetMainSection = 'feinabstimmung';
            } else if (currentStep >= 10) {
                targetMainSection = 'leben';
            }
            
            // Hauptabschnitt wechseln, falls nötig
            if (targetMainSection !== currentMainSection) {
                const mainSectionBtn = document.querySelector(`.main-section-btn[data-section="${targetMainSection}"]`);
                if (mainSectionBtn) {
                    mainSectionBtn.click();
                }
            }
            
            // Aktiven Schritt-Container aktualisieren
            steps.forEach(step => {
                step.classList.remove('active');
            });
            document.getElementById(`step${currentStep}`).classList.add('active');
            
            // Initialisiere den Inhalt des aktuellen Schritts
            initCurrentStepContent(currentStep);
            
            // Zurück-Button für ersten Schritt deaktivieren
            document.querySelectorAll('.nav-btn.back').forEach(btn => {
                btn.disabled = (currentStep === 1);
            });
            
            // Scroll nach oben
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Prüfen, ob weiter geklickt werden kann
            updateNextButtonState();
        }
    }
    
    // Sammle ausgewählte Werte für den aktuellen Schritt
    function collectValuesForStep(step) {
        switch(step) {
            case 1: // Grobauswahl
                selectedValues = [];
                document.querySelectorAll('#step1-values .value-checkbox:checked').forEach(checkbox => {
                    const value = checkbox.getAttribute('data-value');
                    if (value && value.trim() !== '') {
                        selectedValues.push(value);
                    }
                });
                break;
                
            case 2: // Familienwerte
                familyValues = [];
                document.querySelectorAll('#step2-values .value-checkbox:checked').forEach(checkbox => {
                    familyValues.push(checkbox.getAttribute('data-value'));
                });
                break;
                
            case 3: // Familienwerte unter der Lupe
                personalValues = [];
                document.querySelectorAll('#step3-values .value-checkbox:checked').forEach(checkbox => {
                    personalValues.push(checkbox.getAttribute('data-value'));
                });
                // Nicht markierte Werte aus Schritt 1, die keine Familienwerte sind, hinzufügen
                selectedValues.forEach(value => {
                    if (!familyValues.includes(value) && !personalValues.includes(value)) {
                        personalValues.push(value);
                    }
                });
                break;
                
            case 4: // Feinauswahl - Top 10
                top10Values = [];
                document.querySelectorAll('#step4-values .value-checkbox:checked').forEach(checkbox => {
                    top10Values.push(checkbox.getAttribute('data-value'));
                });
                break;
                
            case 5: // Top 3
                top3Values = [];
                document.querySelectorAll('#step5-values .value-checkbox:checked').forEach(checkbox => {
                    top3Values.push(checkbox.getAttribute('data-value'));
                });
                break;
                
            case 6: // Höchster Wert
                topValue = "";
                const selectedRadio = document.querySelector('#step6-values .value-checkbox:checked');
                if (selectedRadio) {
                    topValue = selectedRadio.getAttribute('data-value');
                }
                break;
                
            case 7: // Übersicht und Anpassung - keine Änderungen, nur Anzeige
                break;
                
            case 8: // Definitionen
                valueDefinitions = {};
                // Definitionen für Top 3 Werte sammeln
                top3Values.forEach(value => {
                    const textarea = document.getElementById(`definition-${value}`);
                    if (textarea) {
                        valueDefinitions[value] = textarea.value.trim();
                    }
                });
                break;
                
            case 9: // Übersicht und Anpassung mit Definitionen - keine Änderungen, nur Anzeige
                break;
        }
    }
    
    // ----- Schritt-spezifische Inhalte initialisieren -----
    
    function initCurrentStepContent(step) {
        switch(step) {
            case 1: // Grobauswahl - bereits beim Start initialisiert
                break;
                
            case 2: // Familienwerte
                renderStep2Values();
                break;
                
            case 3: // Familienwerte unter der Lupe
                renderFamilyValuesHotspot();
                renderStep3Values();
                break;
                
            case 4: // Feinauswahl
                renderStep4Values();
                updateSelectedCounter('step4', top10Values.length, 10);
                break;
                
            case 5: // Top 3
                renderTop10ValuesHotspot();
                renderStep5Values();
                updateSelectedCounter('step5', top3Values.length, 3);
                break;
                
            case 6: // Höchster Wert
                renderTop3ValuesHotspot();
                renderStep6Values();
                break;
                
            case 7: // Übersicht und Anpassung
                renderValuesPyramid(7);
                initPyramidEditing(7);
                setupImageSave('save-pyramid-image-7', 'values-pyramid-7', 'Meine_Wertepyramide');
                break;
                
            case 8: // Definitionen
                renderTop1ValueHotspot();
                renderDefinitionFields();
                break;
                
            case 9: // Übersicht und Anpassung mit Definitionen
                renderValuesPyramid(9);
                renderValueDefinitions();
                initPyramidEditing(9);
                setupImageSave('save-pyramid-image-9', 'values-pyramid-9', 'Meine_Wertepyramide_mit_Definitionen');
                break;
        }
    }
    
    // ----- Render-Funktionen für die verschiedenen Schritte -----
    
    // Schritt 2: Familienwerte
    function renderStep2Values() {
        const valuesGrid = document.getElementById('step2-values');
        let html = '';
    
        // Komplette Werteliste anzeigen
        valuesList.sort().forEach(value => {
            const isChecked = familyValues.includes(value) ? 'checked' : '';
            const isSelected = familyValues.includes(value) ? 'selected' : '';
            const wasSelected = selectedValues.includes(value);
            const highlightClass = wasSelected ? 'was-selected' : '';
            
            html += `
                <div class="value-item ${isSelected} ${highlightClass}" data-value="${value}">
                    <input type="checkbox" class="value-checkbox" id="family-${value}" data-value="${value}" ${isChecked}>
                    <label class="value-label" for="family-${value}">${value}</label>
                </div>
            `;
        });
    
        // Benutzerdefinierte Werte hinzufügen
        selectedValues.forEach(value => {
            if (!valuesList.includes(value)) {
                const isChecked = familyValues.includes(value) ? 'checked' : '';
                const isSelected = familyValues.includes(value) ? 'selected' : '';
                
                html += `
                    <div class="value-item ${isSelected}" data-value="${value}">
                        <input type="checkbox" class="value-checkbox" id="family-${value}" data-value="${value}" ${isChecked}>
                        <label class="value-label" for="family-${value}">${value}</label>
                    </div>
                `;
            }
        });
    
        // Leere Zeile für benutzerdefinierte Werte hinzufügen
        html += `
            <div class="value-item custom" data-value="custom-family-${customValueCounter}">
                <input type="checkbox" class="value-checkbox" id="family-custom-${customValueCounter}" data-value="">
                <input type="text" class="value-input" placeholder="Eigener Wert..." data-id="family-${customValueCounter}">
            </div>
        `;
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl
        const valueItems = document.querySelectorAll('#step2-values .value-item:not(.custom)');
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.value-checkbox');
                const isCheckbox = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf die Checkbox geklickt wurde, ändere Checkbox-Status
                if (!isCheckbox) {
                    checkbox.checked = !checkbox.checked;
                }
                
                // Stil der Box anpassen
                if (checkbox.checked) {
                    this.classList.add('selected');
                } else {
                    this.classList.remove('selected');
                }
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    
        // Event-Listener für benutzerdefinierte Werte-Eingabefelder
        setupFamilyCustomValueInputs();
    }
    
    // Event-Listener für benutzerdefinierte Werte-Eingabefelder bei Familienwerten
    function setupFamilyCustomValueInputs() {
        const customInputs = document.querySelectorAll('#step2-values .value-input');
    
        customInputs.forEach(input => {
            // Checkbox automatisch aktivieren, wenn Text eingegeben wird
            input.addEventListener('input', function() {
                const valueItem = this.closest('.value-item');
                const checkbox = valueItem.querySelector('.value-checkbox');
                
                // Checkbox aktualisieren
                if (this.value.trim() !== '') {
                    checkbox.checked = true;
                    valueItem.classList.add('selected');
                    checkbox.setAttribute('data-value', this.value.trim());
                } else {
                    checkbox.checked = false;
                    valueItem.classList.remove('selected');
                    checkbox.setAttribute('data-value', '');
                }
                
                // Neue leere Zeile hinzufügen, wenn etwas eingegeben wurde
                if (this.value.trim() !== '' && !valueItem.classList.contains('filled')) {
                    valueItem.classList.add('filled');
                    addNewFamilyCustomValueInput(valueItem.parentElement);
                }
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
            
            // Checkbox aktivieren/deaktivieren bei Klick
            const valueItem = input.closest('.value-item');
            const checkbox = valueItem.querySelector('.value-checkbox');
            
            valueItem.addEventListener('click', function(e) {
                // Nur reagieren, wenn nicht auf das Eingabefeld geklickt wurde
                if (!e.target.classList.contains('value-input')) {
                    // Prüfen, ob der Input einen Wert hat
                    if (input.value.trim() !== '') {
                        checkbox.checked = !checkbox.checked;
                        
                        if (checkbox.checked) {
                            this.classList.add('selected');
                        } else {
                            this.classList.remove('selected');
                        }
                        
                        // Prüfen, ob weiter geklickt werden kann
                        updateNextButtonState();
                    }
                }
            });
        });
    }
    
    // Neue leere Zeile für benutzerdefinierte Familienwerte hinzufügen
    function addNewFamilyCustomValueInput(container) {
        customValueCounter++;
    
        const newCustomValue = document.createElement('div');
        newCustomValue.className = 'value-item custom';
        newCustomValue.setAttribute('data-value', `custom-family-${customValueCounter}`);
    
        newCustomValue.innerHTML = `
            <input type="checkbox" class="value-checkbox" id="family-custom-${customValueCounter}" data-value="">
            <input type="text" class="value-input" placeholder="Eigener Wert..." data-id="family-${customValueCounter}">
        `;
    
        container.appendChild(newCustomValue);
    
        // Event-Listener für das neue Element hinzufügen
        const input = newCustomValue.querySelector('.value-input');
    
        // Checkbox automatisch aktivieren, wenn Text eingegeben wird
        input.addEventListener('input', function() {
            const valueItem = this.closest('.value-item');
            const checkbox = valueItem.querySelector('.value-checkbox');
            
            // Checkbox aktualisieren
            if (this.value.trim() !== '') {
                checkbox.checked = true;
                valueItem.classList.add('selected');
                checkbox.setAttribute('data-value', this.value.trim());
            } else {
                checkbox.checked = false;
                valueItem.classList.remove('selected');
                checkbox.setAttribute('data-value', '');
            }
            
            // Neue leere Zeile hinzufügen, wenn etwas eingegeben wurde
            if (this.value.trim() !== '' && !valueItem.classList.contains('filled')) {
                valueItem.classList.add('filled');
                addNewFamilyCustomValueInput(valueItem.parentElement);
            }
            
            // Prüfen, ob weiter geklickt werden kann
            updateNextButtonState();
        });
    
        // Checkbox aktivieren/deaktivieren bei Klick
        const valueItem = input.closest('.value-item');
        const checkbox = valueItem.querySelector('.value-checkbox');
    
        valueItem.addEventListener('click', function(e) {
            // Nur reagieren, wenn nicht auf das Eingabefeld geklickt wurde
            if (!e.target.classList.contains('value-input')) {
                // Prüfen, ob der Input einen Wert hat
                if (input.value.trim() !== '') {
                    checkbox.checked = !checkbox.checked;
                    
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                    
                    // Prüfen, ob weiter geklickt werden kann
                    updateNextButtonState();
                }
            }
        });
    }
    
    // Schritt 3: Familienwerte-Hotspot und persönliche Werte
    function renderFamilyValuesHotspot() {
        const familyValuesList = document.getElementById('family-values-list');
        let html = '';
    
        familyValues.forEach(value => {
            html += `<div class="selected-value-tag">${value}</div>`;
        });
    
        if (familyValues.length === 0) {
            html = '<p>Keine Familienwerte ausgewählt.</p>';
        }
    
        familyValuesList.innerHTML = html;
    
        // Auch die übereinstimmenden Werte anzeigen
        const matchingValuesList = document.getElementById('matching-values-list');
        let matchingHtml = '';
    
        // Finde Werte, die sowohl in Familienwerten als auch in ausgewählten Werten enthalten sind
        const matchingValues = selectedValues.filter(value => familyValues.includes(value));
    
        matchingValues.forEach(value => {
            matchingHtml += `<div class="selected-value-tag">${value}</div>`;
        });
    
        if (matchingValues.length === 0) {
            matchingHtml = '<p>Keine übereinstimmenden Werte gefunden.</p>';
        }
    
        matchingValuesList.innerHTML = matchingHtml;
    
        // Edit-Button-Handler
        document.getElementById('edit-familywerte').addEventListener('click', function() {
            // Zum Schritt 2 zurücknavigieren
            const stepButton = document.querySelector('.timeline-button[data-step="2"]');
            if (stepButton) {
                stepButton.click();
            }
        });
    }
    
    // Schritt 3: Familienwerte-Hotspot und persönliche Werte
    function renderStep3Values() {
        const valuesGrid = document.getElementById('step3-values');
        let html = '';
    
        familyValues.forEach(value => {
            // Übereinstimmende Werte sollten vorausgewählt sein
            const matchingValue = selectedValues.includes(value);
            const isChecked = matchingValue ? 'checked' : '';
            const isSelected = matchingValue ? 'selected' : '';
            
            html += `
                <div class="value-item ${isSelected}" data-value="${value}">
                    <input type="checkbox" class="value-checkbox" id="personal-${value}" data-value="${value}" ${isChecked}>
                    <label class="value-label" for="personal-${value}">${value}</label>
                </div>
            `;
        });
    
        // Leere Zeile für benutzerdefinierte Werte hinzufügen
        html += `
            <div class="value-item custom" data-value="custom-personal-${customValueCounter}">
                <input type="checkbox" class="value-checkbox" id="personal-custom-${customValueCounter}" data-value="">
                <input type="text" class="value-input" placeholder="Eigener Wert..." data-id="personal-${customValueCounter}">
            </div>
        `;
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl
        const valueItems = document.querySelectorAll('#step3-values .value-item:not(.custom)');
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.value-checkbox');
                const isCheckbox = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf die Checkbox geklickt wurde, ändere Checkbox-Status
                if (!isCheckbox) {
                    checkbox.checked = !checkbox.checked;
                }
                
                // Stil der Box anpassen
                if (checkbox.checked) {
                    this.classList.add('selected');
                } else {
                    this.classList.remove('selected');
                }
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    
        // Event-Listener für benutzerdefinierte Werte hinzufügen
        setupCustomValueInputs(document.querySelectorAll('#step3-values .value-input'), '#step3-values');
    }
    
    // Schritt 4: Feinauswahl - Top 10
    function renderStep4Values() {
        const valuesGrid = document.getElementById('step4-values');
        let html = '';
    
        personalValues.forEach(value => {
            const isChecked = top10Values.includes(value) ? 'checked' : '';
            const isSelected = top10Values.includes(value) ? 'selected' : '';
            
            html += `
                <div class="value-item ${isSelected}" data-value="${value}">
                    <input type="checkbox" class="value-checkbox" id="top10-${value}" data-value="${value}" ${isChecked}>
                    <label class="value-label" for="top10-${value}">${value}</label>
                </div>
            `;
        });
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl mit maximaler Anzahl
        const maxSelection = 10;
        const valueItems = document.querySelectorAll('#step4-values .value-item');
    
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.value-checkbox');
                const isCheckbox = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf die Checkbox geklickt wurde
                if (!isCheckbox) {
                    // Wenn noch nicht gecheckt und noch Platz
                    if (!checkbox.checked) {
                        const checkedCount = document.querySelectorAll('#step4-values .value-checkbox:checked').length;
                        if (checkedCount < maxSelection) {
                            checkbox.checked = true;
                            this.classList.add('selected');
                        } else {
                            // Maximalanzahl erreicht - Hinweis anzeigen (könnte eine Toast-Nachricht sein)
                            alert(`Du kannst maximal ${maxSelection} Werte auswählen.`);
                            return;
                        }
                    } else {
                        // Checkbox abwählen
                        checkbox.checked = false;
                        this.classList.remove('selected');
                    }
                } else {
                    // Direkt auf Checkbox geklickt
                    // Prüfen, ob Maximum erreicht ist
                    if (checkbox.checked) {
                        const checkedCount = document.querySelectorAll('#step4-values .value-checkbox:checked').length;
                        if (checkedCount > maxSelection) {
                            checkbox.checked = false;
                            e.preventDefault();
                            alert(`Du kannst maximal ${maxSelection} Werte auswählen.`);
                            return;
                        }
                    }
                    
                    // Stil der Box anpassen
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                }
                
                // Counter aktualisieren
                const checkedCount = document.querySelectorAll('#step4-values .value-checkbox:checked').length;
                updateSelectedCounter('step4', checkedCount, maxSelection);
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    }
    
    // Schritt 5: Top 3 Werte
    function renderTop10ValuesHotspot() {
        const top10ValuesList = document.getElementById('top10-values-list');
        let html = '';
    
        top10Values.forEach(value => {
            html += `<div class="selected-value-tag">${value}</div>`;
        });
    
        if (top10Values.length === 0) {
            html = '<p>Keine Top 10 Werte ausgewählt.</p>';
        }
    
        top10ValuesList.innerHTML = html;
    
        // Edit-Button-Handler
        document.getElementById('edit-top10').addEventListener('click', function() {
            // Zum Schritt 4 zurücknavigieren
            const stepButton = document.querySelector('.timeline-button[data-step="4"]');
            if (stepButton) {
                stepButton.click();
            }
        });
    }
    
    function renderStep5Values() {
        const valuesGrid = document.getElementById('step5-values');
        let html = '';
    
        top10Values.forEach(value => {
            const isChecked = top3Values.includes(value) ? 'checked' : '';
            const isSelected = top3Values.includes(value) ? 'selected' : '';
            
            html += `
                <div class="value-item ${isSelected}" data-value="${value}">
                    <input type="checkbox" class="value-checkbox" id="top3-${value}" data-value="${value}" ${isChecked}>
                    <label class="value-label" for="top3-${value}">${value}</label>
                </div>
            `;
        });
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl mit maximaler Anzahl
        const maxSelection = 3;
        const valueItems = document.querySelectorAll('#step5-values .value-item');
    
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.value-checkbox');
                const isCheckbox = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf die Checkbox geklickt wurde
                if (!isCheckbox) {
                    // Wenn noch nicht gecheckt und noch Platz
                    if (!checkbox.checked) {
                        const checkedCount = document.querySelectorAll('#step5-values .value-checkbox:checked').length;
                        if (checkedCount < maxSelection) {
                            checkbox.checked = true;
                            this.classList.add('selected');
                        } else {
                            // Maximalanzahl erreicht - Hinweis anzeigen
                            alert(`Du kannst maximal ${maxSelection} Werte auswählen.`);
                            return;
                        }
                    } else {
                        // Checkbox abwählen
                        checkbox.checked = false;
                        this.classList.remove('selected');
                    }
                } else {
                    // Direkt auf Checkbox geklickt
                    // Prüfen, ob Maximum erreicht ist
                    if (checkbox.checked) {
                        const checkedCount = document.querySelectorAll('#step5-values .value-checkbox:checked').length;
                        if (checkedCount > maxSelection) {
                            checkbox.checked = false;
                            e.preventDefault();
                            alert(`Du kannst maximal ${maxSelection} Werte auswählen.`);
                            return;
                        }
                    }
                    
                    // Stil der Box anpassen
                    if (checkbox.checked) {
                        this.classList.add('selected');
                    } else {
                        this.classList.remove('selected');
                    }
                }
                
                // Counter aktualisieren
                const checkedCount = document.querySelectorAll('#step5-values .value-checkbox:checked').length;
                updateSelectedCounter('step5', checkedCount, maxSelection);
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    }
    
    // Schritt 6: Höchster Wert
    function renderTop3ValuesHotspot() {
        const top3ValuesList = document.getElementById('top3-values-list');
        let html = '';
    
        top3Values.forEach(value => {
            html += `<div class="selected-value-tag">${value}</div>`;
        });
    
        if (top3Values.length === 0) {
            html = '<p>Keine Top 3 Werte ausgewählt.</p>';
        }
    
        top3ValuesList.innerHTML = html;
    
        // Edit-Button-Handler
        document.getElementById('edit-top3').addEventListener('click', function() {
            // Zum Schritt 5 zurücknavigieren
            const stepButton = document.querySelector('.timeline-button[data-step="5"]');
            if (stepButton) {
                stepButton.click();
            }
        });
    }
    
    function renderStep6Values() {
        const valuesGrid = document.getElementById('step6-values');
        let html = '';
    
        top3Values.forEach(value => {
            const isChecked = value === topValue ? 'checked' : '';
            const isSelected = value === topValue ? 'selected' : '';
            
            html += `
                <div class="value-item ${isSelected}" data-value="${value}">
                    <input type="radio" name="top-value" class="value-checkbox" id="top-${value}" data-value="${value}" ${isChecked}>
                    <label class="value-label" for="top-${value}">${value}</label>
                </div>
            `;
        });
    
        valuesGrid.innerHTML = html;
    
        // Event-Listener für Werte-Auswahl - nur einer kann ausgewählt werden
        const valueItems = document.querySelectorAll('#step6-values .value-item');
        valueItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const radio = this.querySelector('.value-checkbox');
                const isRadio = e.target.classList.contains('value-checkbox');
                
                // Wenn nicht direkt auf das Radio geklickt wurde
                if (!isRadio) {
                    radio.checked = true;
                }
                
                // Alle anderen deselektieren
                valueItems.forEach(otherItem => {
                    if (otherItem !== this) {
                        otherItem.classList.remove('selected');
                    }
                });
                
                // Diesen selektieren
                this.classList.add('selected');
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    }
    
    // Schritt 7 & 9: Wertepyramide rendern
    function renderValuesPyramid(step) {
        const stepSuffix = step === 9 ? '-9' : '';
        
        // Top Wert
        const topValueElem = document.getElementById(`pyramid-top-value${stepSuffix}`);
        if (topValueElem) {
            topValueElem.textContent = topValue;
        }
        
        // Top 3 Werte (ohne Top-Wert)
        const top3ValuesElem = document.getElementById(`pyramid-top3-values${stepSuffix}`);
        if (top3ValuesElem) {
            const displayValues = top3Values.filter(v => v !== topValue);
            top3ValuesElem.textContent = displayValues.join(', ');
        }
        
        // Top 10 Werte (ohne Top 3)
        const top10ValuesElem = document.getElementById(`pyramid-top10-values${stepSuffix}`);
        if (top10ValuesElem) {
            const displayValues = top10Values.filter(v => !top3Values.includes(v));
            top10ValuesElem.textContent = displayValues.join(', ');
        }
        
        // Familienwerte (ohne Top 10)
        const familyValuesElem = document.getElementById(`pyramid-family-values${stepSuffix}`);
        if (familyValuesElem) {
            const displayValues = familyValues.filter(v => !top10Values.includes(v));
            familyValuesElem.textContent = displayValues.join(', ');
        }
    }
    
    // Pyramidenbearbeitung initialisieren
    function initPyramidEditing(step) {
        const stepSuffix = step === 9 ? '-9' : '';
        const editButtons = document.querySelectorAll(`.edit-pyramid-section[data-section$="${stepSuffix}"]`);
        
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const sectionType = this.getAttribute('data-section');
                
                // Werte-Array basierend auf Sektionstyp ermitteln
                let values = [];
                let editType = '';
                let excludeValues = [];
                
                if (sectionType === `top-value${stepSuffix}`) {
                    values = [topValue];
                    editType = 'radio';
                    excludeValues = [];
                } else if (sectionType === `top3-values${stepSuffix}`) {
                    values = top3Values;
                    editType = 'checkbox';
                    excludeValues = [topValue];
                } else if (sectionType === `top10-values${stepSuffix}`) {
                    values = top10Values;
                    editType = 'checkbox';
                    excludeValues = top3Values;
                } else if (sectionType === `family-values${stepSuffix}`) {
                    values = familyValues;
                    editType = 'checkbox';
                    excludeValues = top10Values;
                }
                
                // Bearbeitungs-Modal erstellen
                createEditPyramidModal(sectionType, values, editType, excludeValues, step);
            });
        });
    }
    
    // Modal zum Bearbeiten der Pyramidenwerte erstellen
    function createEditPyramidModal(sectionType, values, editType, excludeValues, step) {
        // Sicherheitsüberprüfung
        if (!values || !Array.isArray(values)) {
            values = [];
        }
        if (!excludeValues || !Array.isArray(excludeValues)) {
            excludeValues = [];
        }
        
        // Seitenbezeichnung je nach Abschnitt
        let sectionTitle = '';
        if (sectionType.includes('top-value')) {
            sectionTitle = 'Top Wert bearbeiten';
        } else if (sectionType.includes('top3-values')) {
            sectionTitle = 'Top 3 Werte bearbeiten';
        } else if (sectionType.includes('top10-values')) {
            sectionTitle = 'Top 10 Werte bearbeiten';
        } else if (sectionType.includes('family-values')) {
            sectionTitle = 'Familienwerte bearbeiten';
        }
        
        // Wähle die richtigen Werte aus, um sie im Modal anzuzeigen
        let availableValues = [];
        if (sectionType.includes('top-value')) {
            availableValues = top3Values;
        } else if (sectionType.includes('top3-values')) {
            availableValues = top10Values;
        } else if (sectionType.includes('top10-values')) {
            availableValues = personalValues;
        } else if (sectionType.includes('family-values')) {
            availableValues = valuesList.concat(selectedValues).filter((v, i, a) => a.indexOf(v) === i);
        }
        
        // Erstelle das Modal
        const modalElement = document.createElement('div');
        modalElement.className = 'modal edit-pyramid-modal active';
        modalElement.style.position = 'fixed';
        modalElement.style.top = '0';
        modalElement.style.left = '0';
        modalElement.style.width = '100%';
        modalElement.style.height = '100%';
        modalElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modalElement.style.display = 'flex';
        modalElement.style.justifyContent = 'center';
        modalElement.style.alignItems = 'center';
        modalElement.style.zIndex = '2000';
        
        // Modal-Inhalt
        modalElement.innerHTML = `
            <div class="edit-pyramid-modal-content" style="background-color: white; border-radius: 10px; padding: 20px; max-width: 500px; width: 90%; max-height: 80vh; overflow-y: auto;">
                <h3 style="font-family: 'Montserrat', sans-serif; margin-bottom: 15px;">${sectionTitle}</h3>
                <div class="edit-values-container">
                    <!-- Werte werden hier eingefügt -->
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                    <button class="cancel-edit-btn" style="background-color: #f0f0f0; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">Abbrechen</button>
                    <button class="save-edit-btn" style="background-color: #7c6cd8; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">Speichern</button>
                </div>
            </div>
        `;
        
        // Zum Dokument hinzufügen
        document.body.appendChild(modalElement);
        
        // Werte-Container abrufen
        const valuesContainer = modalElement.querySelector('.edit-values-container');
        
        // Werte anzeigen
        availableValues.forEach(value => {
            // Nicht einbeziehen, wenn in der Ausschlussliste
            if (excludeValues.includes(value)) {
                return;
            }
            
            const isChecked = values.includes(value) ? 'checked' : '';
            const inputType = editType === 'radio' ? 'radio' : 'checkbox';
            const inputName = editType === 'radio' ? 'edit-top-value' : `edit-${sectionType}`;
            
            valuesContainer.innerHTML += `
                <div class="edit-value-item" style="margin-bottom: 10px; display: flex; align-items: center;">
                    <input type="${inputType}" id="edit-${value}" name="${inputName}" value="${value}" ${isChecked} style="margin-right: 10px;">
                    <label for="edit-${value}" style="font-family: 'Open Sans', sans-serif;">${value}</label>
                </div>
            `;
        });
        
        // Abbrechen-Button
        const cancelButton = modalElement.querySelector('.cancel-edit-btn');
        cancelButton.addEventListener('click', function() {
            modalElement.remove();
        });
        
        // Speichern-Button
        const saveButton = modalElement.querySelector('.save-edit-btn');
        saveButton.addEventListener('click', function() {
            // Werte sammeln
            const selectedValues = [];
            const inputs = valuesContainer.querySelectorAll('input:checked');
            inputs.forEach(input => {
                selectedValues.push(input.value);
            });
            
            // Werte aktualisieren
            if (sectionType.includes('top-value')) {
                topValue = selectedValues.length > 0 ? selectedValues[0] : "";
            } else if (sectionType.includes('top3-values')) {
                // Top 3 Werte aktualisieren (inkl. Top-Wert)
                const newTop3 = [topValue, ...selectedValues].filter((v, i, a) => a.indexOf(v) === i).slice(0, 3);
                top3Values = newTop3;
            } else if (sectionType.includes('top10-values')) {
                // Top 10 Werte aktualisieren (inkl. Top 3)
                const newTop10 = [...top3Values, ...selectedValues].filter((v, i, a) => a.indexOf(v) === i).slice(0, 10);
                top10Values = newTop10;
            } else if (sectionType.includes('family-values')) {
                // Familienwerte aktualisieren
                familyValues = selectedValues;
            }
            
            // Pyramide neu rendern
            renderValuesPyramid(step);
            
            // Falls Schritt 9, auch Definitionen neu rendern
            if (step === 9) {
                renderValueDefinitions();
            }
            
            // Modal schließen
            modalElement.remove();
        });
    }
    
    // Schritt 8: Definitionen
    function renderTop1ValueHotspot() {
        const top1ValueList = document.getElementById('top1-value-list');
        let html = '';
    
        if (topValue) {
            html = `<div class="selected-value-tag">${topValue} <span class="edit-value" data-value="${topValue}">✎</span></div>`;
        } else {
            html = '<p>Kein höchster Wert ausgewählt.</p>';
        }
    
        top1ValueList.innerHTML = html;
    
        // Edit-Button-Handler
        document.getElementById('edit-top1').addEventListener('click', function() {
            // Zum Schritt 6 zurücknavigieren
            const stepButton = document.querySelector('.timeline-button[data-step="6"]');
            if (stepButton) {
                stepButton.click();
            }
        });
    
        // Event-Listener für Bleistift-Symbol beim höchsten Wert
        const editValueBtn = top1ValueList.querySelector('.edit-value');
        if (editValueBtn) {
            editValueBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const valueTag = this.parentElement;
                const value = this.getAttribute('data-value');
                
                // Formular zum Bearbeiten anzeigen
                const editForm = document.createElement('div');
                editForm.className = 'value-edit-form';
                editForm.innerHTML = `
                    <input type="text" class="value-edit-input" value="${value}">
                    <button class="value-save-btn">✓</button>
                `;
                
                // Ersetze den Tag durch das Formular
                valueTag.style.display = 'none';
                valueTag.parentElement.appendChild(editForm);
                
                // Fokus auf das Eingabefeld
                const input = editForm.querySelector('.value-edit-input');
                input.focus();
                
                // Event-Listener für das Speichern
                const saveBtn = editForm.querySelector('.value-save-btn');
                saveBtn.addEventListener('click', function() {
                    const newValue = input.value.trim();
                    if (newValue !== '') {
                        // Aktualisiere den Wert
                        const oldValue = value;
                        topValue = newValue;
                        
                        // Aktualisiere das UI
                        valueTag.innerHTML = newValue + ` <span class="edit-value" data-value="${newValue}">✎</span>`;
                        valueTag.style.display = 'flex';
                        editForm.remove();
                        
                        // Aktualisiere Definitionen
                        if (valueDefinitions[oldValue]) {
                            valueDefinitions[newValue] = valueDefinitions[oldValue];
                            delete valueDefinitions[oldValue];
                        }
                        
                        // Top 3 aktualisieren
                        const index = top3Values.indexOf(oldValue);
                        if (index !== -1) {
                            top3Values[index] = newValue;
                        }
                        
                        // Definitionen neu rendern
                        renderDefinitionFields();
                    }
                });
                
                // Event-Listener für Enter-Taste
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        saveBtn.click();
                    }
                });
            });
        }
    }
    
    function renderDefinitionFields() {
        const definitionContainer = document.getElementById('definition-container');
        let html = '';
    
        // Definitionen für alle Top-3-Werte anzeigen
        top3Values.forEach(value => {
            const definition = valueDefinitions[value] || '';
            const isHighlighted = value === topValue ? 'value-definition-highlight' : '';
            
            html += `
                <div class="value-definition ${isHighlighted}">
                    <h3 class="value-definition-title">${value} <span class="edit-value" data-value="${value}">✎</span></h3>
                    <p>Was bedeutet "${value}" für dich persönlich?</p>
                    <textarea id="definition-${value}" placeholder="Beschreibe, was dieser Wert für dich bedeutet...">${definition}</textarea>
                </div>
            `;
        });
    
        definitionContainer.innerHTML = html;
    
        // Event-Listener für Textareas
        const textareas = document.querySelectorAll('#definition-container textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', function() {
                // Definitionen aktualisieren
                const valueId = this.id.replace('definition-', '');
                valueDefinitions[valueId] = this.value.trim();
                
                // Prüfen, ob weiter geklickt werden kann
                updateNextButtonState();
            });
        });
    
        // Event-Listener für Bleistift-Symbole
        const editValueBtns = definitionContainer.querySelectorAll('.edit-value');
        editValueBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const title = this.parentElement;
                const value = this.getAttribute('data-value');
                
                // Formular zum Bearbeiten anzeigen
                const editForm = document.createElement('div');
                editForm.className = 'value-edit-form';
                editForm.innerHTML = `
                    <input type="text" class="value-edit-input" value="${value}">
                    <button class="value-save-btn">✓</button>
                `;
                
                // Ersetze den Titel durch das Formular
                title.style.display = 'none';
                title.parentElement.insertBefore(editForm, title.nextSibling);
                
                // Fokus auf das Eingabefeld
                const input = editForm.querySelector('.value-edit-input');
                input.focus();
                
                // Event-Listener für das Speichern
                const saveBtn = editForm.querySelector('.value-save-btn');
                saveBtn.addEventListener('click', function() {
                    const newValue = input.value.trim();
                    if (newValue !== '') {
                        // Aktualisiere den Wert
                        const oldValue = value;
                        
                        // Aktualisiere top3Values
                        const index = top3Values.indexOf(oldValue);
                        if (index !== -1) {
                            top3Values[index] = newValue;
                        }
                        
                        // Aktualisiere topValue falls nötig
                        if (topValue === oldValue) {
                            topValue = newValue;
                        }
                        
                        // Aktualisiere valueDefinitions
                        if (valueDefinitions[oldValue]) {
                            valueDefinitions[newValue] = valueDefinitions[oldValue];
                            delete valueDefinitions[oldValue];
                        }
                        
                        // Aktualisiere das UI
                        title.innerHTML = newValue + ` <span class="edit-value" data-value="${newValue}">✎</span>`;
                        title.style.display = 'block';
                        
                        // Aktualisiere Beschreibungselement
                        const paragraph = title.nextElementSibling;
                        if (paragraph.tagName === 'P') {
                            paragraph.innerHTML = `Was bedeutet "${newValue}" für dich persönlich?`;
                        }
                        
                        // Aktualisiere Textarea-ID
                        const textarea = title.parentElement.querySelector('textarea');
                        if (textarea) {
                            textarea.id = `definition-${newValue}`;
                        }
                        
                        editForm.remove();
                        
                        // Aktualisiere Top1ValueHotspot
                        renderTop1ValueHotspot();
                    }
                });
                
                // Event-Listener für Enter-Taste
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        saveBtn.click();
                    }
                });
            });
        });
    }
    
    // Wertdefinitionen in Schritt 9 rendern
    function renderValueDefinitions() {
        const definitionsContainer = document.getElementById('value-definitions-container');
        if (!definitionsContainer) return;
        
        let html = '';
        
        // Zuerst den höchsten Wert
        if (topValue && valueDefinitions[topValue]) {
            html += `
                <div class="definition-item">
                    <h4 class="definition-value">${topValue}</h4>
                    <p class="definition-text">${valueDefinitions[topValue]}</p>
                </div>
            `;
        }
        
        // Dann die anderen Top 3 Werte
        top3Values.filter(v => v !== topValue).forEach(value => {
            if (valueDefinitions[value]) {
                html += `
                    <div class="definition-item">
                        <h4 class="definition-value">${value}</h4>
                        <p class="definition-text">${valueDefinitions[value]}</p>
                    </div>
                `;
            }
        });
        
        if (html === '') {
            html = '<p style="color: #777;">Keine Wertedefinitionen vorhanden.</p>';
        }
        
        definitionsContainer.innerHTML = html;
    }
    
    // Bild-Speichern-Funktion einrichten
    function setupImageSave(buttonId, containerId, filename) {
        const saveButton = document.getElementById(buttonId);
        if (!saveButton) return;
        
        saveButton.addEventListener('click', function() {
            const containerElement = document.getElementById(containerId);
            if (!containerElement) return;
            
            // Temporäre Klasse hinzufügen, um Edit-Buttons auszublenden
            containerElement.classList.add('saving-image');
            
            // Edit-Buttons ausblenden für den Screenshot
            const editButtons = containerElement.querySelectorAll('.edit-pyramid-section');
            editButtons.forEach(button => {
                button.style.display = 'none';
            });
            
            // Screenshot erstellen
            html2canvas(containerElement, {
                backgroundColor: 'white',
                scale: 2 // Für bessere Qualität
            }).then(canvas => {
                // Bild herunterladen
                const link = document.createElement('a');
                link.download = `${filename}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
                
                // Edit-Buttons wieder einblenden
                editButtons.forEach(button => {
                    button.style.display = 'flex';
                });
                
                // Temporäre Klasse entfernen
                containerElement.classList.remove('saving-image');
            });
        });
    }
    
    // ----- Hilfsfunktionen -----
    
    // Aktualisiere den Zähler für ausgewählte Werte
    function updateSelectedCounter(step, count, max) {
        const counterElement = document.getElementById(`selected-counter-${step}`);
        if (counterElement) {
            counterElement.textContent = `${count} von ${max} Werten ausgewählt`;
            
            if (count === max) {
                counterElement.style.color = '#4caf50'; // Grün
            } else if (count > 0) {
                counterElement.style.color = '#2196f3'; // Blau
            } else {
                counterElement.style.color = '#f44336'; // Rot
            }
        }
    }
    
    // Prüfen, ob der Weiter-Button aktiviert werden kann
    function updateNextButtonState() {
        const currentStepElement = document.querySelector('.step-container.active');
        if (!currentStepElement) return;
    
        const stepNumber = parseInt(currentStepElement.id.replace('step', ''));
        const nextButton = document.getElementById(`step${stepNumber}-next`);
    
        if (!nextButton) return;
    
        let canProceed = false;
    
        switch(stepNumber) {
            case 1: // Grobauswahl - Mindestens 1 Wert
                canProceed = document.querySelectorAll('#step1-values .value-checkbox:checked').length > 0;
                break;
                
            case 2: // Familienwerte - Keine Bedingung, kann immer weiter
                canProceed = true;
                break;
                
            case 3: // Familienwerte unter der Lupe - kann immer weiter
                canProceed = true;
                break;
                
            case 4: // Feinauswahl - Maximal 10, aber nicht 0
                const step4Count = document.querySelectorAll('#step4-values .value-checkbox:checked').length;
                canProceed = step4Count > 0 && step4Count <= 10;
                break;
                
            case 5: // Top 3 - Genau 3 Werte
                const step5Count = document.querySelectorAll('#step5-values .value-checkbox:checked').length;
                canProceed = step5Count === 3;
                break;
                
            case 6: // Höchster Wert - Genau 1 Wert
                canProceed = document.querySelector('#step6-values .value-checkbox:checked') !== null;
                break;
                
            case 7: // Übersicht und Anpassung
                canProceed = true;
                break;
                
            case 8: // Definitionen
                canProceed = true;
                break;
                
            case 9: // Übersicht und Anpassung mit Definitionen
                canProceed = true;
                break;
                
            case 10: // Bestandsaufnahme
                canProceed = true;
                break;
                
            case 11: // Richtungsentscheidungen - kein Weiter-Button
                canProceed = false;
                break;
        }
    
        nextButton.disabled = !canProceed;
    }
    
    // ----- Initialisierung -----
    
    // Wertebegriffe initialisieren
    initValuesList();
    
    // Navigation initialisieren
    initNavigation();
    
    // Prüfen, ob Weiter-Button aktiviert werden kann
    updateNextButtonState();
    });