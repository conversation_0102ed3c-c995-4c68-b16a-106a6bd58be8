// search.js - Suchfunktionalität für die Website

// Levenshtein-Distanz-Funktion für fuzzy search
function levenshteinDistance(a, b) {
    if (a.length === 0) return b.length;
    if (b.length === 0) return a.length;

    const matrix = [];

    // Initialisiere Matrix
    for (let i = 0; i <= b.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
        matrix[0][j] = j;
    }

    // Fülle Matrix
    for (let i = 1; i <= b.length; i++) {
        for (let j = 1; j <= a.length; j++) {
            if (b.charAt(i - 1) === a.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1, // Substitution
                    matrix[i][j - 1] + 1,     // Einfügung
                    matrix[i - 1][j] + 1      // Löschung
                );
            }
        }
    }

    return matrix[b.length][a.length];
}

// Ähnlichkeit zwischen zwei Wörtern berechnen (0 bis 1, wobei 1 = identisch)
function similarity(a, b) {
    const distance = levenshteinDistance(a.toLowerCase(), b.toLowerCase());
    const maxLength = Math.max(a.length, b.length);
    return 1 - (distance / maxLength);
}

// Initialisiere Suchfunktion
function initSearch() {
    // DOM-Elemente vorbereiten
    const header = document.querySelector('.header');
    header.style.display = 'flex';
    header.style.justifyContent = 'center';
    header.style.padding = '0 15px';
    header.style.position = 'relative';
    
    // Suchleiste erstellen
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.style.position = 'absolute';
    searchContainer.style.left = '15px';
    searchContainer.style.top = '50%';
    searchContainer.style.transform = 'translateY(-50%)';
    
    searchContainer.innerHTML = `
        <div class="search-input-wrapper">
            <input type="text" id="search-input" placeholder="Begriff suchen..." class="search-input" autocomplete="off">
            <button id="clear-search" class="clear-search" style="display: none;">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
            <button id="search-button" class="search-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
            </button>
        </div>
    `;
    
    header.appendChild(searchContainer);
    
    // Such-Modal erstellen
    const searchResultsModal = document.createElement('div');
    searchResultsModal.id = 'search-results-modal';
    searchResultsModal.className = 'modal';
    searchResultsModal.innerHTML = `
        <div class="modal-content search-results-modal-content">
            <button class="close-modal">&times;</button>
            <div id="search-results-content" class="content-scroll">
                <!-- Suchergebnisse werden hier eingefügt -->
            </div>
        </div>
    `;
    
    document.body.appendChild(searchResultsModal);
    
    // Elemente und Daten für die Suche
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    const clearButton = document.getElementById('clear-search');
    const searchInputWrapper = document.querySelector('.search-input-wrapper');
    const searchableContent = collectSearchableContent();
    
    // Mobilansicht: Suchleiste ein-/ausklappen
    let isSearchExpanded = false;
    
    // Prüfen, ob mobile Ansicht vorliegt
    function isMobileView() {
        return window.innerWidth <= 768;
    }
    
    // Suchleiste ein-/ausklappen basierend auf Viewport
    function toggleSearchVisibility() {
        if (isMobileView()) {
            if (isSearchExpanded) {
                searchInputWrapper.classList.add('expanded');
            } else {
                searchInputWrapper.classList.remove('expanded');
            }
        } else {
            // Auf Desktop immer eingeblendet
            searchInputWrapper.classList.add('expanded');
        }
    }
    
    // Initialen Zustand setzen
    toggleSearchVisibility();
    
    // Event-Handler für Lupen-Icon zum Ein-/Ausklappen
    searchButton.addEventListener('click', function(e) {
        if (isMobileView()) {
            if (!isSearchExpanded) {
                isSearchExpanded = true;
                toggleSearchVisibility();
                searchInput.focus();
                e.stopPropagation();
                return;
            }
        }
        // Falls die Suche bereits erweitert ist oder Desktop-Ansicht vorliegt, normal suchen
        executeSearch();
    });
    
    // Klick außerhalb der Suche in mobilem Modus schließt Suchleiste
    document.addEventListener('click', function(e) {
        if (isMobileView() && isSearchExpanded) {
            if (!searchContainer.contains(e.target) && !searchResultsModal.contains(e.target)) {
                isSearchExpanded = false;
                toggleSearchVisibility();
            }
        }
    });
    
    // Bei Größenänderung des Fensters Suchleisten-Sichtbarkeit anpassen
    window.addEventListener('resize', function() {
        toggleSearchVisibility();
    });
    
    // Event-Handler-Funktionen
    const updateClearButton = () => {
        clearButton.style.display = searchInput.value.trim() ? 'block' : 'none';
    };
    
    const executeSearch = () => {
        const query = searchInput.value.trim();
        if (query && query.length >= 2) {
            const results = performSearch(query, searchableContent);
            displaySearchResults(results, query);
        } else {
            searchResultsModal.classList.remove('active');
        }
    };
    
    // Event-Listener
    searchInput.addEventListener('input', (e) => {
        updateClearButton();
        executeSearch();
        e.stopPropagation();
    });
    
    clearButton.addEventListener('click', (e) => {
        searchInput.value = '';
        updateClearButton();
        searchInput.focus();
        searchResultsModal.classList.remove('active');
        e.stopPropagation();
    });
    
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            executeSearch();
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    searchResultsModal.querySelector('.close-modal').addEventListener('click', (e) => {
        searchResultsModal.classList.remove('active');
        e.stopPropagation();
        e.preventDefault();
    });
    
    // Klick außerhalb zum Schließen des Such-Modals
    document.addEventListener('click', (e) => {
        const isClickInSearchElements = 
            e.target.closest('#search-results-modal .modal-content') !== null || 
            e.target.closest('#search-button') !== null ||
            e.target.closest('#search-input') !== null ||
            e.target.closest('#clear-search') !== null ||
            e.target.closest('.search-input-wrapper') !== null;
        
        if (!isClickInSearchElements && searchResultsModal.classList.contains('active')) {
            searchResultsModal.classList.remove('active');
        }
    });
    
    // Event-Listener für Ergebnisauswahl
    document.addEventListener('click', event => {
        const resultItem = event.target.closest('.search-result-item');
        if (resultItem) {
            handleResultSelection(event);
        }
    });
    
    // CSS-Styles für die Suchfunktion hinzufügen
    addSearchStyles();
}

// Sammel alle durchsuchbaren Inhalte
function collectSearchableContent() {
    const searchableContent = [];
    
    // Inhalte aus den Modals
    modalsData.forEach(modal => {
        const plainContent = modal.content.replace(/<[^>]*>?/gm, ' ');
        searchableContent.push({
            id: modal.id,
            title: modal.title,
            content: plainContent,
            type: 'modal',
            snippet: plainContent.substring(0, 150) + '...'
        });
    });
    
    // Inhalte aus den Slides
    slidesData.forEach(slide => {
        searchableContent.push({
            id: slide.id,
            title: slide.title.replace(/<br>/g, ' '),
            content: slide.altText,
            type: 'slide',
            snippet: slide.altText
        });
    });
    
    // Über Mich Inhalt
    const ueberMichContent = document.querySelector('.ueber-mich-modal-content .content-scroll');
    if (ueberMichContent) {
        searchableContent.push({
            id: 'ueber-mich-modal',
            title: 'Über Mich',
            content: ueberMichContent.textContent,
            type: 'page',
            snippet: 'Persönliche Informationen über Jana Sophie Breitmar'
        });
    }
    
    // Impressum Inhalt
    const impressumContent = document.querySelector('.impressum-modal-content .content-scroll');
    if (impressumContent) {
        searchableContent.push({
            id: 'impressum-modal',
            title: 'Impressum & Datenschutz',
            content: impressumContent.textContent,
            type: 'page',
            snippet: 'Rechtliche Informationen und Datenschutzerklärung'
        });
    }
    
    return searchableContent;
}

// Suche durchführen
function performSearch(query, searchableContent) {
    if (!query || query.trim() === '') return [];
    
    const results = [];
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
    const SIMILARITY_THRESHOLD = 0.6;
    
    searchableContent.forEach(item => {
        let score = 0;
        const content = (item.title + ' ' + item.content).toLowerCase();
        const contentWords = content.split(/\s+|\.|,|:|;|-|\(|\)/).filter(word => word.length > 1);
        
        queryTerms.forEach(term => {
            // Exakte Übereinstimmung
            if (content.includes(term)) {
                score += 2;
            }
            
            // Ähnliche Wörter finden
            contentWords.forEach(word => {
                const wordSimilarity = similarity(term, word);
                if (wordSimilarity >= SIMILARITY_THRESHOLD) {
                    score += wordSimilarity;
                }
            });
        });
        
        if (score > 0) {
            results.push({
                ...item,
                score: score
            });
        }
    });
    
    // Sortiere Ergebnisse nach Score (absteigend)
    return results.sort((a, b) => b.score - a.score);
}

// Highlight-Funktion für Suchergebnisse
function highlightSearchTerms(text, query) {
    if (!query || query.trim() === '') return text;
    
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 1);
    let highlightedText = text;
    
    queryTerms.forEach(term => {
        const regex = new RegExp('(' + term + ')', 'gi');
        highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });
    
    return highlightedText;
}

// Suchergebnisse anzeigen
function displaySearchResults(results, query) {
    const searchResultsModal = document.getElementById('search-results-modal');
    const searchResultsContent = document.getElementById('search-results-content');
    
    if (!searchResultsModal || !searchResultsContent) return;
    
    // Leere bisherige Ergebnisse
    searchResultsContent.innerHTML = '';
    
    if (results.length === 0) {
        searchResultsContent.innerHTML = `
            <div class="no-results">
                <p>Keine Ergebnisse für "<strong>${query}</strong>" gefunden.</p>
                <p>Versuche es mit anderen Suchbegriffen oder weniger spezifischen Begriffen.</p>
            </div>
        `;
    } else {
        // Ergebnistitel
        const resultsTitle = document.createElement('h2');
        resultsTitle.textContent = `${results.length} Ergebnis${results.length !== 1 ? 'se' : ''} für "${query}"`;
        searchResultsContent.appendChild(resultsTitle);
        
        // Ergebnisliste
        const resultsList = document.createElement('div');
        resultsList.className = 'search-results-list';
        
        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';
            resultItem.setAttribute('data-id', result.id);
            resultItem.setAttribute('data-type', result.type);
            
            // Icon je nach Ergebnistyp
            let iconClass = '';
            switch(result.type) {
                case 'modal': iconClass = 'modal-icon'; break;
                case 'slide': iconClass = 'slide-icon'; break;
                case 'page': iconClass = 'page-icon'; break;
            }
            
            resultItem.innerHTML = `
                <div class="result-header">
                    <span class="result-icon ${iconClass}"></span>
                    <h3 class="result-title">${highlightSearchTerms(result.title, query)}</h3>
                </div>
                <p class="result-snippet">${highlightSearchTerms(result.snippet, query)}</p>
            `;
            
            resultsList.appendChild(resultItem);
        });
        
        searchResultsContent.appendChild(resultsList);
    }
    
    // Modal öffnen
    searchResultsModal.classList.add('active');
}

// Suchergebnisauswahl verarbeiten
function handleResultSelection(event) {
    const resultItem = event.target.closest('.search-result-item');
    if (!resultItem) return;
    
    const itemId = resultItem.getAttribute('data-id');
    const itemType = resultItem.getAttribute('data-type');
    
    // Suchergebnis-Modal schließen
    document.getElementById('search-results-modal').classList.remove('active');
    
    // Je nach Typ des Ergebnisses eine Aktion ausführen
    switch(itemType) {
        case 'modal':
            // Finde den zugehörigen Hotspot und klicke ihn an
            const modalData = modalsData.find(modal => modal.id === itemId);
            if (modalData) {
                const matchingHotspots = [];
                slidesData.forEach((slide, slideIndex) => {
                    slide.hotspots.forEach(hotspot => {
                        if (hotspot.modalId === itemId) {
                            matchingHotspots.push({
                                hotspotId: hotspot.id,
                                slideIndex: slideIndex
                            });
                        }
                    });
                });
                
                if (matchingHotspots.length > 0) {
                    // Nehme den ersten passenden Hotspot
                    const target = matchingHotspots[0];
                    
                    // Navigiere zum Slide
                    const slides = document.querySelectorAll('.carousel-slide');
                    const dots = document.querySelectorAll('.carousel-dot');
                    
                    // Entferne aktive Klasse von allen Slides
                    slides.forEach(slide => slide.classList.remove('active'));
                    
                    // Setze den gewünschten Slide als aktiv
                    slides[target.slideIndex].classList.add('active');
                    
                    // Aktualisiere auch die Navigations-Dots
                    dots.forEach((dot, i) => {
                        if (i === target.slideIndex) {
                            dot.classList.add('active');
                        } else {
                            dot.classList.remove('active');
                        }
                    });
                    
                    // Warte kurz, dann klicke den Hotspot
                    setTimeout(() => {
                        const hotspotElement = document.getElementById(target.hotspotId);
                        if (hotspotElement) {
                            hotspotElement.click();
                        }
                    }, 500);
                }
            }
            break;
            
        case 'slide':
            // Navigiere zum entsprechenden Slide
            const slideIndex = slidesData.findIndex(slide => slide.id === itemId);
            if (slideIndex >= 0) {
                // Direkter Zugriff auf die DOM-Elemente
                const slides = document.querySelectorAll('.carousel-slide');
                const dots = document.querySelectorAll('.carousel-dot');
                
                // Alle Slides deaktivieren
                slides.forEach(slide => slide.classList.remove('active'));
                
                // Gewünschten Slide aktivieren
                slides[slideIndex].classList.add('active');
                
                // Navigations-Dots aktualisieren
                dots.forEach((dot, i) => {
                    if (i === slideIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }
            break;
            
        case 'page':
            // Öffne das entsprechende Seiten-Modal
            document.getElementById(itemId).classList.add('active');
            break;
    }
}

// CSS-Styles für die Suchfunktion
function addSearchStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Suchleisten-Styling */
        .search-container {
            display: flex;
            align-items: center;
            height: 28px;
            max-width: 200px;
            border-radius: 14px;
            background-color: rgba(255, 255, 255, 0.15);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .search-container:focus-within {
            background-color: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 5px rgba(124, 108, 216, 0.5);
        }
        
        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .search-input {
            width: 160px;
            height: 100%;
            border: none;
            padding: 0 30px 0 10px; /* Abstand rechts für X-Button */
            font-size: 13px;
            color: white;
            background: transparent;
            outline: none;
            transition: width 0.3s ease, opacity 0.3s ease;
            font-family: 'Open Sans', Arial, sans-serif;
        }
        
        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
            font-family: 'Open Sans', Arial, sans-serif;
        }
        
        .clear-search {
            position: absolute;
            right: 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.5);
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.2s ease;
            width: 14px;
            height: 14px;
            z-index: 2;
        }
        
        .clear-search:hover {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .search-button {
            width: 28px;
            height: 28px;
            border: none;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
            z-index: 2;
        }
        
        .search-button:hover {
            color: white;
        }
        
        /* Mobile Suchleisten-Styling */
        @media (max-width: 768px) {
            .search-input-wrapper {
                width: 28px;
                overflow: hidden;
            }
            
            .search-input-wrapper.expanded {
                width: 100%;
            }
            
            .search-input {
                opacity: 0;
                position: absolute;
                left: 0;
                width: calc(100% - 28px);
            }
            
            .search-input-wrapper.expanded .search-input {
                opacity: 1;
                position: relative;
            }
            
            .clear-search {
                opacity: 0;
                pointer-events: none;
            }
            
            .search-input-wrapper.expanded .clear-search {
                opacity: 1;
                pointer-events: auto;
            }
        }
        
        /* Suchergebnisse Modal Styling - DEZENTERE VERSION */
        #search-results-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            display: none;
            z-index: 1050;
            pointer-events: none;
        }

        #search-results-modal.active {
            display: block;
            pointer-events: auto;
        }

        .search-results-modal-content {
            position: absolute;
            top: 40px; /* Direkt unter der Suchleiste */
            left: 15px; /* Gleiche Position wie die Suchleiste */
            transform: none;
            max-width: 550px;
            width: 90%;
            max-height: 80vh;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        #search-results-modal.active .search-results-modal-content {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Verbesserte, dezentere Styles für den Inhalt des Suchergebnisse-Modals */
        #search-results-content {
            padding: 18px;
            overflow-y: auto;
            max-height: calc(80vh - 20px);
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
            padding-right: 10px;
        }
        
        #search-results-content::-webkit-scrollbar {
            width: 5px;
            position: absolute;
            right: 0;
        }
        
        #search-results-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.03);
            border-radius: 3px;
            margin-top: 5px;
            margin-bottom: 5px;
        }
        
        #search-results-content::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.15);
            border-radius: 3px;
        }
        
        .search-results-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 15px;
            width: 100%;
        }
        
        .search-result-item {
            padding: 12px;
            border-radius: 6px;
            background-color: rgba(124, 108, 216, 0.03);
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 2px solid transparent;
            width: 100%;
        }
        
        .search-result-item:hover {
            background-color: rgba(124, 108, 216, 0.07);
            border-left-color: rgba(124, 108, 216, 0.5);
            transform: translateX(2px);
        }
        
        .result-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .result-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
            opacity: 0.7;
        }
        
        .modal-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%237c6cd8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>');
        }
        
        .slide-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%237c6cd8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>');
        }
        
        .page-icon {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%237c6cd8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
        }
        
        .result-title {
            font-size: 15px;
            font-weight: 500;
            color: #444;
            margin: 0;
            font-family: 'Montserrat', sans-serif;
        }
        
        .result-snippet {
            font-size: 13px;
            color: #666;
            margin: 0;
            line-height: 1.4;
            font-family: 'Open Sans', Arial, sans-serif;
        }
        
        .no-results {
            text-align: center;
            padding: 18px 0;
            color: #777;
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 14px;
        }
        
        mark {
            background-color: rgba(124, 108, 216, 0.12);
            color: #444;
            padding: 0 1px;
            border-radius: 2px;
        }
        
        /* Anpassung für den Schließen-Button im Suchergebnisse-Modal */
        #search-results-modal .close-modal {
            position: absolute; 
            top: 8px;
            right: 8px;
            z-index: 1060;
        }
        
        #search-results-modal h2 {
            font-size: 16px;
            color: #555;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .search-container {
                max-width: 150px;
            }
            
            .search-input {
                width: 110px;
                font-size: 13px;
            }
            
            .search-results-modal-content {
                width: calc(100% - 30px);
                max-width: none;
            }
        }
    `;
    
    document.head.appendChild(style);
}

// Nach DOM-Ladung die Suchfunktion initialisieren
document.addEventListener('DOMContentLoaded', () => {
    // Warten, bis die Hauptfunktionalität geladen ist
    setTimeout(initSearch, 1000);
});