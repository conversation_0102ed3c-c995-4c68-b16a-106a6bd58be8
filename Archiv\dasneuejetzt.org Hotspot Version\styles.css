/* styles.css */

/* caveat-regular - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Caveat';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* caveat-700 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Caveat';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }


/* montserrat-300 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 300;
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* montserrat-regular - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* montserrat-600 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* montserrat-700 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }


  /* open-sans-300 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 300;
    src: url('assets/fonts/open-sans-v40-latin-300.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* open-sans-regular - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: url('assets/fonts/open-sans-v40-latin-regular.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* open-sans-600 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 600;
    src: url('assets/fonts/open-sans-v40-latin-600.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* open-sans-700 - latin */
  @font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: url('assets/fonts/open-sans-v40-latin-700.woff2') format('woff2'); /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }

html {
    height: 100%;
    overflow: hidden;
    position: fixed;
    width: 100%;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', Arial, sans-serif;
}

body {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #222222;
    touch-action: none; /* Verhindert alle Touch-Gesten */
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
}

/* Allgemeine Elemente */
h2 {
    margin-bottom: 15px;
    color: #333;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

p {
    line-height: 1.6;
    margin-bottom: 20px;
    font-family: 'Open Sans', Arial, sans-serif;
    color: #444;
}

/* Header und Footer */
.header, .footer {
    background-color: #222222;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: none;
    flex-shrink: 0;
}

.header {
    height: 40px;
    font-size: 18px;
    font-family: 'Montserrat', sans-serif;
}

.header-link {
    color: #7c6cd8;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.footer {
    height: 40px;
    font-size: 14px;
    padding: 0 15px;
    text-align: center;
    flex-wrap: wrap;
    color: #7c6cd8;
    border-bottom: 0.5px solid #7c6cd8; /* Hier ist der lila Strich */
}

.footer a {
    color: #7c6cd8;
    text-decoration: none;
    margin: 0 10px;
}

.footer a:hover {
    text-decoration: underline;
}

/* Main container und Carousel */
.main-image-container {
    position: relative;
    flex-grow: 1;
    width: 100%;
    height: calc(100% - 80px); /* Statt 100vh - 80px */
    overflow: hidden;
    background-color: white;
}

.carousel-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.carousel-slide {
    width: 100%;
    height: 100%;
    position: absolute;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    pointer-events: none; /* Deaktiviert Interaktionen für nicht aktive Slides */
}

.carousel-slide.active {
    opacity: 1;
    pointer-events: auto; /* Aktiviert Interaktionen nur für den aktiven Slide */
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    object-position: center center;
}

/* Bildpositionierungen für verschiedene Slides */
#img-zwischenmenschlichkeit { object-position: center 30%; }
#img-emotionalitaet { object-position: center 7%; }
#img-schoepferkraft { object-position: center 40%; }
#img-zusammenhalt { object-position: center 5%; }
#img-fuehrung { object-position: center 5%; }

/* Carousel Navigation */
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 50px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s;
    z-index: 10;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5), 0 0 10px rgba(0, 0, 0, 0.3);
    -webkit-text-stroke: 2px white;
    background-color: transparent;
}

.carousel-arrow:hover, .carousel-arrow:focus {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0 8px rgba(0, 0, 0, 0.7), 0 0 15px rgba(0, 0, 0, 0.5);
    outline: none;
}

.prev-arrow { left: 30px; }
.next-arrow { right: 30px; }

.carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s;
}

.carousel-dot.active, .carousel-dot:hover, .carousel-dot:focus {
    background-color: white;
}

.carousel-dot:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* Slide Titel */
.category-title {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 36px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.5);
    -webkit-text-stroke: 1px white;
    background-color: transparent;
    padding: 10px 20px;
    z-index: 5;
    font-family: 'Montserrat', sans-serif;
    letter-spacing: 2px;
    line-height: 1.5;
    text-transform: uppercase;
}

/* Hotspots */
.hotspot {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    transition: transform 0.3s, background-color 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 5;
    animation: pulse 2s infinite;
    touch-action: manipulation; /* Verhindert Zoom bei Doppeltipp */
}

/* Spezieller letzter Hotspot */
#hotspot-fuehrung-1 {
    width: 120px;
    height: 120px;
    background-color: rgba(124, 108, 216, 0.8);
    color: white;
    font-size: 48px;
    animation: pulseStrong 1.5s infinite;
}

#hotspot-fuehrung-1:hover, #hotspot-fuehrung-1:focus {
    transform: scale(1.1);
    background-color: rgba(124, 108, 216, 0.9);
    animation: none;
}

.hotspot:hover, .hotspot:focus {
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 0.9);
    animation: none;
    outline: none;
}

.hotspot.active {
    background-color: #7c6cd8;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(124, 108, 216, 0.6);
    animation: none;
    z-index: 6;
}

/* Fokus-Stile für Tastaturnavigation */
.hotspot:focus-visible,
.carousel-arrow:focus-visible,
.carousel-dot:focus-visible,
.social-link:focus-visible,
.cta-button:focus-visible,
a:focus-visible {
    outline: 3px solid #7c6cd8;
    outline-offset: 3px;
    box-shadow: 0 0 0 3px rgba(124, 108, 216, 0.5);
    animation: none;
}

/* Dunkler Hintergrund erfordert helleren Fokus */
.header-link:focus-visible,
.footer a:focus-visible {
    outline-color: white;
}

/* Hotspot-Pulse-Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
}

/* Stärkere Pulsanimation für den letzten Hotspot */
@keyframes pulseStrong {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 20px rgba(124, 108, 216, 0.4);
    }
    50% {
        transform: scale(1.08);
        box-shadow: 0 2px 30px rgba(124, 108, 216, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 20px rgba(124, 108, 216, 0.4);
    }
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    display: none;
    z-index: 1010;
    pointer-events: none;
}

.modal.preparing {
    display: block;
    background-color: transparent;
}

.modal.active {
    display: block;
    pointer-events: auto;
    background-color: transparent;
}

.modal.page-modal.active {
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 30px;
    max-width: 550px; /* Vergrößert für bessere Lesbarkeit */
    width: 85%;
    max-height: 80vh;
    overflow-y: auto;
    border-radius: 10px;
    position: absolute;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    z-index: 1015;
    touch-action: pan-y;
}

/* Sprechblasen-Verbindungsstück - tatsächliches DOM-Element */
.modal-connector {
    position: fixed;
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.95); /* Gleiche Farbe wie Modal */
    transform: rotate(45deg);
    z-index: 1011; /* Wichtig: Muss unter modal-content (1015) aber über der modal (1010) sein */
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    display: none; /* Standardmäßig ausgeblendet */
    pointer-events: none; /* Damit Klicks hindurchgehen */
}

/* Scrollbar für alle scrollbaren Elemente */
.modal-content::-webkit-scrollbar,
.content-scroll::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track,
.content-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb,
.content-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

/* Modal Animationen */
.modal:not(.preparing) .modal-content {
    transition: transform 0.3s, opacity 0.3s;
}

.modal:not(.active) .modal-content {
    transform: scale(0.9);
    opacity: 0;
}

.modal.active .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* Spezielle Modals (Page-Modals) */
.impressum-modal-content,
.ueber-mich-modal-content {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.content-scroll {
    padding: 30px;
    overflow-y: auto;
    max-height: calc(80vh - 60px);
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
}

.modal.page-modal:not(.active) .impressum-modal-content,
.modal.page-modal:not(.active) .ueber-mich-modal-content,
.modal.page-modal:not(.active) .enlarged-image-modal-content {
    transform: translate(-50%, -55%) scale(0.9);
    opacity: 0;
}

.modal.page-modal.active .impressum-modal-content,
.modal.page-modal.active .ueber-mich-modal-content,
.modal.page-modal.active .enlarged-image-modal-content {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

/* Schließen-Button für Modals */
.close-modal {
    position: sticky;
    top: 0;
    right: 10px;
    float: right;
    font-size: 24px;
    cursor: pointer;
    background: none;
    border: none;
    color: #555;
    transition: color 0.2s;
    z-index: 10;
    margin-left: 10px;
    margin-bottom: 5px;
    touch-action: manipulation;
}

.close-modal:hover, .close-modal:focus {
    color: #000;
    outline: none;
}

/* CTA-Button in Modals */
.cta-button {
    display: inline-block;
    background-color: transparent;
    color: #7c6cd8;
    padding: 12px 24px; 
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
    touch-action: manipulation;
    font-size: 16px;
    margin-top: 10px;
}

/* Spezieller CTA-Button für den ersten Hotspot */
#modal-zwischenmenschlichkeit-0 .cta-button {
    border: none;
    padding-left: 0;
}

#modal-zwischenmenschlichkeit-0 .cta-button:hover, 
#modal-zwischenmenschlichkeit-0 .cta-button:focus {
    transform: translateX(5px);
}

/* Spezieller CTA-Button für den letzten Hotspot */
#modal-fuehrung-1 .cta-button {
    border: 1px solid #7c6cd8;
}

#modal-fuehrung-1 .cta-button:hover, 
#modal-fuehrung-1 .cta-button:focus {
    color: #6658b8;
    border-color: #6658b8;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(124, 108, 216, 0.1);
}

.cta-button:hover, 
.cta-button:focus {
    color: #6658b8;
    outline: none;
}

/* Styles für Sektionen in Modals */
.section {
    margin-bottom: 40px;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 24px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.section p, .section li {
    line-height: 1.6;
    margin-bottom: 15px;
    font-family: 'Open Sans', Arial, sans-serif;
    color: #444;
}

/* Profil-Header im Über-mich-Modal */
.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    text-align: center;
}

.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 3px solid #7c6cd8;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.profile-image:hover, .profile-image:focus {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    outline: none;
}

.profile-name {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 28px;
    margin-bottom: 10px;
    color: #333;
}

.profile-title {
    font-family: 'Caveat', cursive;
    font-size: 26px;
    color: #7c6cd8;
    margin-bottom: 15px;
    font-weight: 600;
}

/* Zitate und Skills in Über-mich-Modal */
.quote {
    font-style: italic;
    color: #666;
    padding: 15px;
    border-left: 3px solid #7c6cd8;
    margin: 20px 0;
    background-color: rgba(124, 108, 216, 0.05);
    font-family: 'Open Sans', Arial, sans-serif;
}

.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.skill-label {
    display: inline-block;
    background-color: rgba(124, 108, 216, 0.2);
    color: #6658b8;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    border: 1px solid rgba(124, 108, 216, 0.3);
    font-family: 'Open Sans', Arial, sans-serif;
}

.skill-label:hover {
    background-color: rgba(124, 108, 216, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(124, 108, 216, 0.2);
}

/* Social Links */
.social-links {
    display: flex;
    justify-content: flex-start; /* Links ausgerichtet statt zentriert */
    gap: 25px;
    margin-top: 10px;
}

.social-link {
    color: #7c6cd8;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s;
    padding: 8px 15px;
    border-radius: 5px;
    font-family: 'Open Sans', Arial, sans-serif;
}

.social-link:hover, .social-link:focus {
    color: #6658b8;
    text-decoration: underline;
    outline: none;
}

/* Vergrößertes Bild Modal */
.enlarged-image-modal-content {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    width: auto;
    max-height: 90vh;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
}

.enlarged-image {
    display: block;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 5px;
}

/* Suchcontainer Styling */
.search-container {
    display: flex;
    align-items: center;
    height: 28px;
    max-width: 180px;
    border-radius: 14px;
    background-color: rgba(255, 255, 255, 0.15);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-container:focus-within {
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 5px rgba(124, 108, 216, 0.5);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    transition: all 0.3s ease;
}

.search-input {
    width: 140px;
    height: 100%;
    border: none;
    padding: 0 25px 0 10px;
    font-size: 13px;
    color: #7c6cd8;
    background: transparent;
    outline: none;
    transition: width 0.3s ease, opacity 0.3s ease;
    font-family: 'Open Sans', Arial, sans-serif;
}

.search-input::placeholder {
    color: rgba(124, 108, 216, 0.6);
    font-family: 'Open Sans', Arial, sans-serif;
}

.clear-search {
    position: absolute;
    right: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: rgba(124, 108, 216, 0.7);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    width: 14px;
    height: 14px;
    z-index: 2;
}

.clear-search:hover, .clear-search:focus {
    color: rgba(124, 108, 216, 1);
    outline: none;
}

.search-button {
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(124, 108, 216, 0.7);
    transition: color 0.3s ease;
    z-index: 2;
}

.search-button:hover, .search-button:focus {
    color: #7c6cd8;
    outline: none;
}

/* Verbesserte Textlesbarkeit in Modals */
.modal-content h2 {
    font-size: 22px; /* Größere Überschrift */
    margin-bottom: 20px;
    color: #444;
}

.modal-content p {
    font-size: 16px; /* Größere Schrift */
    line-height: 1.7; /* Erhöhter Zeilenabstand */
    margin-bottom: 18px;
}

.modal-content strong {
    color: #333;
    font-weight: 600;
}

/* YouTube Video Container */
.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    max-width: 100%;
    margin-bottom: 20px;
}

.video-container a {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.video-container a:hover, .video-container a:focus {
    transform: scale(1.02);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    outline: none;
}

.video-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.01);
}

/* Tastaturnavigation-Support */
.skip-to-content {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.skip-to-content:focus {
    position: fixed;
    top: 0;
    left: 0;
    width: auto;
    height: auto;
    padding: 10px 15px;
    background-color: #fff;
    z-index: 9999;
    border-bottom-right-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    outline: none;
    color: #7c6cd8;
    font-weight: bold;
}

/* Dezentere Suchergebnisse Styling */
#search-results-modal {
    background-color: rgba(0, 0, 0, 0.2);
}

.search-results-modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

#search-results-content {
    padding: 18px;
    scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
}

#search-results-content::-webkit-scrollbar {
    width: 5px;
}

#search-results-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
}

#search-results-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.15);
}

.search-results-list {
    gap: 12px;
    margin-top: 15px;
}

.search-result-item {
    padding: 12px;
    border-radius: 6px;
    background-color: rgba(124, 108, 216, 0.03);
    border-left: 2px solid transparent;
}

.search-result-item:hover {
    background-color: rgba(124, 108, 216, 0.07);
    border-left-color: rgba(124, 108, 216, 0.5);
    transform: translateX(2px);
}

.result-header {
    margin-bottom: 6px;
}

.result-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    opacity: 0.7;
}

.result-title {
    font-size: 15px;
    font-weight: 500;
    color: #444;
}

.result-snippet {
    font-size: 13px;
    color: #666;
}

.no-results {
    padding: 18px 0;
    color: #777;
    font-size: 14px;
}

mark {
    background-color: rgba(124, 108, 216, 0.12);
    color: #444;
    padding: 0 1px;
}

#search-results-modal h2 {
    font-size: 16px;
    color: #555;
    font-weight: 500;
    margin-bottom: 10px;
}

/* Für das Profil-Bild-Modal */
.profile-image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    touch-action: none;
}

.profile-image-modal.active {
    opacity: 1;
    pointer-events: auto;
}

.modal-image-container {
    position: absolute;
    transform: translate(-50%, -50%);
    touch-action: none;
}

/* Responsive Designs */
@media (max-width: 768px) {
    .modal-content {
        width: 90%;
        max-width: 400px;
        padding: 25px;
    }

    .impressum-modal-content,
    .ueber-mich-modal-content {
        width: 95%;
        max-width: 95%;
    }

    .enlarged-image-modal-content {
        width: 95%;
        padding: 15px;
    }

    .hotspot {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }
    
    #hotspot-fuehrung-1 {
        width: 96px;
        height: 96px;
        font-size: 36px;
    }

    .footer {
        height: auto;
        padding: 10px;
        font-size: 12px;
    }

    .carousel-arrow {
        width: 60px;
        height: 60px;
        font-size: 40px;
    }

    .carousel-dots {
        bottom: 10px;
    }

    .carousel-dot {
        width: 10px;
        height: 10px;
    }

    .category-title {
        font-size: 22px;
        top: 30px;
        letter-spacing: 1px;
    }

    h2 {
        font-size: 18px;
    }

    p {
        font-size: 14px;
    }

    .section-title {
        font-size: 20px;
    }
    
    .profile-image {
        width: 100px;
        height: 100px;
    }
    
    .profile-name {
        font-size: 22px;
    }
    
    .profile-title {
        font-size: 18px;
    }
    
    .quote {
        padding: 10px;
        margin: 15px 0;
    }
    
    /* Mobile Suchleisten-Styling */
    .search-input-wrapper {
        width: 28px;
        overflow: hidden;
    }
    
    .search-input-wrapper.expanded {
        width: 100%;
    }
    
    .search-input {
        opacity: 0;
        position: absolute;
        left: 0;
        width: calc(100% - 28px);
    }
    
    .search-input-wrapper.expanded .search-input {
        opacity: 1;
        position: relative;
    }
    
    .clear-search {
        opacity: 0;
        pointer-events: none;
    }
    
    .search-input-wrapper.expanded .clear-search {
        opacity: 1;
        pointer-events: auto;
    }
    
    .search-results-modal-content {
        width: calc(100% - 30px);
        max-width: none;
    }
}